/* 
 * ERP Dashboard - Login Page Styles
 * أنماط صفحة تسجيل الدخول
 */

/* ==================== Login Container ==================== */

.login-wrapper {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.login-wrapper::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 20s infinite ease-in-out;
}

.login-wrapper::after {
    content: '';
    position: absolute;
    bottom: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 25s infinite ease-in-out reverse;
}

@keyframes float {
    0%, 100% {
        transform: translate(0, 0) rotate(0deg);
    }
    33% {
        transform: translate(30px, -30px) rotate(120deg);
    }
    66% {
        transform: translate(-20px, 20px) rotate(240deg);
    }
}

/* ==================== Login Container ==================== */

.login-container {
    width: 100%;
    max-width: 450px;
    position: relative;
    z-index: 1;
}

/* ==================== Login Card ==================== */

.login-card {
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    padding: var(--spacing-2xl);
    backdrop-filter: blur(10px);
}

/* ==================== Login Header ==================== */

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.login-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-3xl);
    box-shadow: var(--shadow-lg);
}

.login-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.login-subtitle {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin: 0;
}

/* ==================== Login Form ==================== */

.login-form {
    margin-bottom: var(--spacing-lg);
}

.login-form .form-group {
    margin-bottom: var(--spacing-lg);
}

.login-form .form-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.login-form .form-control {
    padding: 0.875rem 1rem;
    font-size: var(--font-size-base);
    border-radius: var(--radius-lg);
    border: 2px solid var(--border-color);
    transition: var(--transition-fast);
}

.login-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
}

.input-group {
    position: relative;
}

.input-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: var(--font-size-lg);
    pointer-events: none;
}

.input-group .form-control {
    padding-right: 3rem;
}

.password-toggle {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: 0;
    transition: var(--transition-fast);
}

.password-toggle:hover {
    color: var(--primary-color);
}

/* ==================== Form Options ==================== */

.form-options {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.form-check {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.form-check-input {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    cursor: pointer;
    user-select: none;
}

.forgot-password {
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-medium);
}

.forgot-password:hover {
    text-decoration: underline;
}

/* ==================== Login Button ==================== */

.login-btn {
    width: 100%;
    padding: 1rem;
    font-size: var(--font-size-base);
    font-weight: var(--font-semibold);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* ==================== Login Footer ==================== */

.login-footer {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.login-footer-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.login-footer-link {
    color: var(--primary-color);
    font-weight: var(--font-semibold);
    text-decoration: none;
}

.login-footer-link:hover {
    text-decoration: underline;
}

/* ==================== Social Login ==================== */

.social-login {
    margin-top: var(--spacing-lg);
}

.social-login-divider {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.social-login-divider::before,
.social-login-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: var(--border-color);
}

.social-login-text {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.social-login-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: var(--transition-fast);
}

.social-btn:hover {
    border-color: var(--primary-color);
    background-color: var(--bg-secondary);
}

/* ==================== Alert Messages ==================== */

.login-alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.login-alert-error {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.login-alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

/* ==================== Loading State ==================== */

.login-btn .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: white;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ==================== Responsive ==================== */

@media (max-width: 575.98px) {
    .login-wrapper {
        padding: var(--spacing-md);
    }
    
    .login-card {
        padding: var(--spacing-xl);
    }
    
    .login-title {
        font-size: var(--font-size-xl);
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .social-login-buttons {
        grid-template-columns: 1fr;
    }
}

