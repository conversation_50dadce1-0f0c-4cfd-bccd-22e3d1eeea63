<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام ERP</title>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="../assets/libs/bootstrap/css/bootstrap.rtl.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="../assets/libs/fontawesome/css/all.min.css">
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="../assets/libs/sweetalert2/sweetalert2.min.css">
    
    <!-- Toastr -->
    <link rel="stylesheet" href="../assets/libs/toastr/toastr.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/variables.css">
    <link rel="stylesheet" href="../assets/css/fonts.css">
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/sidebar.css">
    <link rel="stylesheet" href="../assets/css/navbar.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    
    <style>
        /* Statistics Cards */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card-products {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card-products:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .stat-icon.primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.success { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
        .stat-icon.warning { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
        .stat-icon.danger { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }
        
        .stat-details {
            flex: 1;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
        }
        
        .stat-value {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1f2937;
        }
        
        /* Filter Section */
        .filter-section {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .filter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .view-toggle {
            display: flex;
            gap: 0.5rem;
        }
        
        .view-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #e5e7eb;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .view-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .view-btn:hover:not(.active) {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        /* Products Grid View */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
        }
        
        .product-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        }
        
        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }
        
        .product-body {
            padding: 1.25rem;
        }
        
        .product-category {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background: #e0e7ff;
            color: #4f46e5;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .product-name {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .product-description {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .product-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid #e5e7eb;
        }
        
        .product-price {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .product-stock {
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .product-stock.in-stock {
            color: #10b981;
        }
        
        .product-stock.low-stock {
            color: #f59e0b;
        }
        
        .product-stock.out-of-stock {
            color: #ef4444;
        }
        
        .product-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        /* Products Table View */
        .products-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .products-table table {
            width: 100%;
            margin: 0;
        }
        
        .products-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
            padding: 1rem;
            text-align: right;
        }
        
        .products-table td {
            padding: 1rem;
            vertical-align: middle;
            border-top: 1px solid #e5e7eb;
        }
        
        .products-table tbody tr:hover {
            background: #f9fafb;
        }
        
        .product-img-small {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            object-fit: cover;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }
        
        /* Hidden class for view toggle */
        .hidden {
            display: none !important;
        }
        
        /* Pagination */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding: 1rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Wrapper -->
    <div class="wrapper">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <!-- Sidebar Toggle Button -->
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-chevron-left"></i>
            </button>

            <!-- Sidebar Brand -->
            <div class="sidebar-brand">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="sidebar-brand-text">نظام ERP</div>
            </div>

            <!-- Sidebar Navigation -->
            <nav class="sidebar-nav">
                <!-- Main Section -->
                <div class="nav-section">
                    <div class="nav-section-title">القائمة الرئيسية</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="index.html" class="nav-link active">
                                <i class="nav-link-icon fas fa-home"></i>
                                <span class="nav-link-text">لوحة التحكم</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-home"></i>
                                        <span>لوحة التحكم</span>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-chart-line"></i>
                                <span class="nav-link-text">التقارير</span>
                                <span class="badge badge-primary nav-link-badge">5</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-chart-line"></i>
                                        <span>التقارير</span>
                                        <span class="badge bg-primary" style="margin-right: auto;">5</span>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-bell"></i>
                                <span class="nav-link-text">الإشعارات</span>
                                <span class="badge badge-danger nav-link-badge">12</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-bell"></i>
                                        <span>الإشعارات</span>
                                        <span class="badge bg-danger" style="margin-right: auto;">12</span>
                                    </div>
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Management Section -->
                <div class="nav-section">
                    <div class="nav-section-title">الإدارة</div>
                    <ul class="nav-list">
                        <li class="nav-item has-submenu">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-users"></i>
                                <span class="nav-link-text">إدارة الموظفين</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-users"></i>
                                        <span>إدارة الموظفين</span>
                                    </div>
                                    <ul class="nav-popup-submenu">
                                        <li>
                                            <a href="#">
                                                <i class="fas fa-list"></i>
                                                <span>قائمة الموظفين</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#">
                                                <i class="fas fa-user-plus"></i>
                                                <span>إضافة موظف</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#">
                                                <i class="fas fa-clock"></i>
                                                <span>الحضور والانصراف</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </a>
                            <ul class="submenu">
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <span class="nav-link-text">قائمة الموظفين</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <span class="nav-link-text">إضافة موظف</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <span class="nav-link-text">الحضور والانصراف</span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="nav-item has-submenu">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-box"></i>
                                <span class="nav-link-text">إدارة المخزون</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-box"></i>
                                        <span>إدارة المخزون</span>
                                    </div>
                                    <ul class="nav-popup-submenu">
                                        <li>
                                            <a href="#">
                                                <i class="fas fa-boxes"></i>
                                                <span>المنتجات</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#">
                                                <i class="fas fa-warehouse"></i>
                                                <span>المخازن</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </a>
                            <ul class="submenu">
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <span class="nav-link-text">المنتجات</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <span class="nav-link-text">المخازن</span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-wallet"></i>
                                <span class="nav-link-text">المالية</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-wallet"></i>
                                        <span>المالية</span>
                                    </div>
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Pages Section -->
                <div class="nav-section">
                    <div class="nav-section-title">الصفحات</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="pos.html" class="nav-link">
                                <i class="nav-link-icon fas fa-cash-register"></i>
                                <span class="nav-link-text">نقطة البيع POS</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-cash-register"></i>
                                        <span>نقطة البيع POS</span>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="products.html" class="nav-link">
                                <i class="nav-link-icon fas fa-box"></i>
                                <span class="nav-link-text">المنتجات</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-box"></i>
                                        <span>المنتجات</span>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="invoices.html" class="nav-link">
                                <i class="nav-link-icon fas fa-file-invoice"></i>
                                <span class="nav-link-text">الفواتير</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-file-invoice"></i>
                                        <span>الفواتير</span>
                                    </div>
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Demo Pages Section -->
                <div class="nav-section">
                    <div class="nav-section-title">صفحات العرض</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="components-demo.html" class="nav-link">
                                <i class="nav-link-icon fas fa-puzzle-piece"></i>
                                <span class="nav-link-text">عرض المكونات</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="jquery-demo.html" class="nav-link">
                                <i class="nav-link-icon fab fa-js"></i>
                                <span class="nav-link-text">عرض jQuery</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="sample-page.html" class="nav-link">
                                <i class="nav-link-icon fas fa-file"></i>
                                <span class="nav-link-text">صفحة نموذجية</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Settings Section -->
                <div class="nav-section">
                    <div class="nav-section-title">الإعدادات</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-cog"></i>
                                <span class="nav-link-text">إعدادات النظام</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-user-shield"></i>
                                <span class="nav-link-text">الصلاحيات</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

        </aside>

        <!-- Main Content -->
        <div class="main-content" id="mainContent">
            <!-- Navbar -->
            <nav class="navbar" id="navbar">
                <div class="navbar-start">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title">إدارة المنتجات</h1>
                </div>

                <div class="navbar-end">
                    <div class="navbar-item">
                        <button class="btn btn-primary" id="btnAddProduct">
                            <i class="fas fa-plus"></i> إضافة منتج
                        </button>
                    </div>
                    <div class="navbar-item navbar-user">
                        <button class="user-button" id="userMenuBtn">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <span class="user-name">مدير النظام</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="navbar-dropdown" id="userMenu">
                            <a href="../login.html" class="dropdown-item">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Content -->
            <div class="content-wrapper">
                <div class="container-fluid">
                    
                    <!-- Statistics Cards -->
                    <div class="stats-container">
                        <div class="stat-card-products">
                            <div class="stat-icon primary">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="stat-details">
                                <div class="stat-label">إجمالي المنتجات</div>
                                <div class="stat-value" id="totalProducts">150</div>
                            </div>
                        </div>
                        
                        <div class="stat-card-products">
                            <div class="stat-icon success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-details">
                                <div class="stat-label">متوفر في المخزون</div>
                                <div class="stat-value" id="inStockProducts">120</div>
                            </div>
                        </div>
                        
                        <div class="stat-card-products">
                            <div class="stat-icon warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-details">
                                <div class="stat-label">مخزون منخفض</div>
                                <div class="stat-value" id="lowStockProducts">25</div>
                            </div>
                        </div>
                        
                        <div class="stat-card-products">
                            <div class="stat-icon danger">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="stat-details">
                                <div class="stat-label">نفذ من المخزون</div>
                                <div class="stat-value" id="outOfStockProducts">5</div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Section -->
                    <div class="filter-section">
                        <div class="filter-header">
                            <h5 class="mb-0"><i class="fas fa-filter"></i> البحث والفلترة</h5>
                            <div class="view-toggle">
                                <button class="view-btn active" id="gridViewBtn" title="عرض البطاقات">
                                    <i class="fas fa-th"></i>
                                </button>
                                <button class="view-btn" id="tableViewBtn" title="عرض الجدول">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" id="searchInput" placeholder="ابحث عن منتج...">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الفئة</label>
                                <select class="form-select" id="categoryFilter">
                                    <option value="">جميع الفئات</option>
                                    <option value="electronics">إلكترونيات</option>
                                    <option value="clothing">ملابس</option>
                                    <option value="food">أغذية</option>
                                    <option value="furniture">أثاث</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">حالة المخزون</label>
                                <select class="form-select" id="stockFilter">
                                    <option value="">الكل</option>
                                    <option value="in-stock">متوفر</option>
                                    <option value="low-stock">مخزون منخفض</option>
                                    <option value="out-of-stock">نفذ من المخزون</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">السعر</label>
                                <select class="form-select" id="priceFilter">
                                    <option value="">جميع الأسعار</option>
                                    <option value="0-100">0 - 100 ر.س</option>
                                    <option value="100-500">100 - 500 ر.س</option>
                                    <option value="500-1000">500 - 1000 ر.س</option>
                                    <option value="1000+">أكثر من 1000 ر.س</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-primary" id="applyFilters">
                                <i class="fas fa-search"></i> تطبيق الفلاتر
                            </button>
                            <button class="btn btn-secondary" id="resetFilters">
                                <i class="fas fa-redo"></i> إعادة تعيين
                            </button>
                            <button class="btn btn-success" id="exportBtn">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                        </div>
                    </div>

                    <!-- Products Grid View -->
                    <div id="productsGrid" class="products-grid">
                        <!-- Products will be loaded here by JavaScript -->
                    </div>

                    <!-- Products Table View -->
                    <div id="productsTable" class="products-table hidden">
                        <table>
                            <thead>
                                <tr>
                                    <th>الصورة</th>
                                    <th>اسم المنتج</th>
                                    <th>الفئة</th>
                                    <th>السعر</th>
                                    <th>المخزون</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- Products will be loaded here by JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="pagination-container">
                        <div>
                            عرض <strong id="showingFrom">1</strong> إلى <strong id="showingTo">12</strong> من <strong id="totalItems">150</strong> منتج
                        </div>
                        <nav>
                            <ul class="pagination mb-0" id="pagination">
                                <!-- Pagination will be generated by JavaScript -->
                            </ul>
                        </nav>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../assets/libs/jquery/jquery.min.js"></script>
    <script src="../assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/libs/sweetalert2/sweetalert2.min.js"></script>
    <script src="../assets/libs/toastr/toastr.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/products.js"></script>
</body>
</html>

