/**
 * Invoices Page Styles
 * أنماط صفحة الفواتير
 */

/* ==================== Invoice Templates Grid ==================== */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.template-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

.template-preview {
    height: 300px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 4rem;
    position: relative;
    overflow: hidden;
}

.template-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%, rgba(255,255,255,0.1)),
                linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%, rgba(255,255,255,0.1));
    background-size: 60px 60px;
    background-position: 0 0, 30px 30px;
}

.template-preview i {
    position: relative;
    z-index: 1;
}

.template-body {
    padding: 1.5rem;
}

.template-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.template-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1rem;
}

.template-features {
    list-style: none;
    padding: 0;
    margin: 0 0 1rem 0;
}

.template-features li {
    font-size: 0.875rem;
    color: #4b5563;
    padding: 0.25rem 0;
}

.template-features li i {
    color: #10b981;
    margin-left: 0.5rem;
}

.template-actions {
    display: flex;
    gap: 0.5rem;
}

/* ==================== Print Styles ==================== */
@media print {
    /* Hide everything except invoice */
    body * {
        visibility: hidden;
    }

    /* Show only invoice content */
    .invoice-print-area,
    .invoice-print-area * {
        visibility: visible !important;
    }

    /* Position invoice for printing */
    .invoice-print-area {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        margin: 0;
        padding: 20px;
    }

    /* Hide UI elements */
    .swal2-actions,
    .swal2-close,
    button,
    .no-print {
        display: none !important;
        visibility: hidden !important;
    }

    /* Ensure proper page breaks */
    .invoice-container {
        page-break-inside: avoid;
        box-shadow: none;
    }

    /* Fix table printing */
    table {
        page-break-inside: auto;
    }

    tr {
        page-break-inside: avoid;
        page-break-after: auto;
    }

    /* Ensure colors print */
    * {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
}

/* ==================== Invoice Styles ==================== */
.invoice-container {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    max-width: 900px;
    margin: 2rem auto;
}

.invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: start;
    padding-bottom: 2rem;
    border-bottom: 3px solid var(--primary-color);
    margin-bottom: 2rem;
}

.company-info h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.invoice-meta {
    text-align: left;
}

.invoice-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.invoice-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.detail-section h5 {
    color: #374151;
    margin-bottom: 1rem;
    font-weight: 600;
}

.detail-section p {
    margin: 0.25rem 0;
    color: #6b7280;
}

.invoice-table {
    width: 100%;
    margin-bottom: 2rem;
}

.invoice-table th {
    background: #f9fafb;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
}

.invoice-table td {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.invoice-table tbody tr:hover {
    background: #f9fafb;
}

.invoice-totals {
    margin-right: auto;
    width: 300px;
}

.total-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
}

.total-row.grand-total {
    border-top: 2px solid #e5e7eb;
    margin-top: 0.5rem;
    padding-top: 1rem;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.invoice-footer {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #e5e7eb;
    text-align: center;
    color: #6b7280;
}

.invoice-notes {
    background: #f9fafb;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.invoice-notes h6 {
    color: #374151;
    margin-bottom: 0.5rem;
}

/* ==================== Different Invoice Styles ==================== */

/* Modern Style */
.invoice-modern .invoice-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin: -2rem -2rem 2rem -2rem;
}

.invoice-modern .company-info h2,
.invoice-modern .invoice-number {
    color: white;
}

/* Minimal Style */
.invoice-minimal {
    border: 2px solid #e5e7eb;
}

.invoice-minimal .invoice-header {
    border-bottom: 1px solid #e5e7eb;
}

/* Colorful Style */
.invoice-colorful .invoice-table th {
    background: var(--primary-color);
    color: white;
}

/* Elegant Style */
.invoice-elegant {
    font-family: 'Cairo', serif;
}

.invoice-elegant .invoice-header {
    border-bottom: 3px double var(--primary-color);
}

/* ==================== Receipt/Grocery Style ==================== */
.invoice-receipt {
    max-width: 400px;
    margin: 0 auto;
    font-family: 'Courier New', monospace;
    background: white;
    padding: 1rem;
}

.invoice-receipt .invoice-header {
    text-align: center;
    border-bottom: 2px dashed #000;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.invoice-receipt .company-info h2 {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
}

.invoice-receipt .company-info p {
    font-size: 0.875rem;
    margin: 0.125rem 0;
}

.invoice-receipt .invoice-meta {
    text-align: center;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.invoice-receipt .invoice-table {
    margin-bottom: 1rem;
}

.invoice-receipt .invoice-table th,
.invoice-receipt .invoice-table td {
    padding: 0.25rem 0;
    font-size: 0.875rem;
    border: none;
}

.invoice-receipt .invoice-table thead {
    border-bottom: 1px dashed #000;
}

.invoice-receipt .invoice-table tbody tr {
    border-bottom: 1px dotted #ccc;
}

.invoice-receipt .invoice-totals {
    width: 100%;
    border-top: 2px dashed #000;
    padding-top: 0.5rem;
}

.invoice-receipt .total-row {
    font-size: 0.875rem;
}

.invoice-receipt .total-row.grand-total {
    font-size: 1.25rem;
    border-top: 2px solid #000;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
}

.invoice-receipt .invoice-footer {
    text-align: center;
    border-top: 2px dashed #000;
    padding-top: 1rem;
    margin-top: 1rem;
    font-size: 0.75rem;
}

.invoice-receipt .barcode {
    text-align: center;
    margin: 1rem 0;
    font-family: 'Libre Barcode 128', cursive;
    font-size: 3rem;
    letter-spacing: 0;
}

