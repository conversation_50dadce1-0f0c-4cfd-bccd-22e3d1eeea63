/**
 * Modal Animations
 * حركات النوافذ المنبثقة المخصصة
 */

/* ==================== Slide Animation ==================== */
.modal.slide .modal-dialog {
    transform: translateY(-100%);
    transition: transform 0.3s ease-out;
}

.modal.slide.show .modal-dialog {
    transform: translateY(0);
}

/* ==================== Zoom Animation ==================== */
.modal.zoom .modal-dialog {
    transform: scale(0.1);
    transition: transform 0.3s ease-out;
}

.modal.zoom.show .modal-dialog {
    transform: scale(1);
}

/* ==================== Flip Animation ==================== */
.modal.flip .modal-dialog {
    transform: perspective(1000px) rotateX(-90deg);
    transition: transform 0.4s ease-out;
}

.modal.flip.show .modal-dialog {
    transform: perspective(1000px) rotateX(0);
}

/* ==================== Bounce Animation ==================== */
@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.modal.bounce.show .modal-dialog {
    animation: bounceIn 0.5s ease-out;
}

