# نظام إدارة موارد المؤسسة - قالب لوحة التحكم
# ERP System - Admin Dashboard Template

<div dir="rtl">

## 📋 نظرة عامة

قالب لوحة تحكم احترافي وحديث لنظام إدارة موارد المؤسسة (ERP) مبني باستخدام أحدث تقنيات الويب مع دعم كامل للغة العربية والتخطيط من اليمين إلى اليسار (RTL).

## ✨ المميزات

- ✅ **دعم كامل للغة العربية** - واجهة مستخدم باللغة العربية بالكامل
- ✅ **تخطيط RTL** - دعم كامل للتخطيط من اليمين إلى اليسار
- ✅ **تصميم متجاوب** - يعمل بسلاسة على جميع الأجهزة (سطح المكتب، الأجهزة اللوحية، الهواتف)
- ✅ **مكتبات محلية** - جميع المكتبات محملة محلياً بدون اعتماد على CDN
- ✅ **خط Cairo** - استخدام خط Cairo الاحترافي للعربية
- ✅ **رسوم بيانية تفاعلية** - باستخدام Chart.js
- ✅ **إشعارات جميلة** - باستخدام SweetAlert2 و Toastr
- ✅ **أيقونات Font Awesome** - مكتبة أيقونات شاملة
- ✅ **Bootstrap 5** - إطار عمل حديث ومرن

## 🛠️ التقنيات المستخدمة

- **HTML5** - هيكل الصفحات
- **CSS3** - التنسيقات والأنماط
- **JavaScript (ES6+)** - البرمجة والتفاعل
- **jQuery 3.7.1** - معالجة DOM والأحداث
- **Bootstrap 5.3.2** - إطار العمل الأساسي
- **Chart.js 4.4.0** - الرسوم البيانية
- **SweetAlert2** - نوافذ الحوار والتنبيهات
- **Toastr** - إشعارات Toast
- **Font Awesome 6** - الأيقونات
- **Cairo Font** - الخط العربي

## 📁 هيكل المشروع

```
erp-template/
├── assets/
│   ├── css/
│   │   ├── variables.css      # متغيرات CSS
│   │   ├── fonts.css          # تعريفات الخطوط
│   │   ├── main.css           # الأنماط الرئيسية
│   │   ├── sidebar.css        # أنماط الشريط الجانبي
│   │   ├── navbar.css         # أنماط شريط التنقل
│   │   ├── dashboard.css      # أنماط لوحة التحكم
│   │   └── login.css          # أنماط صفحة تسجيل الدخول
│   ├── js/
│   │   ├── main.js            # JavaScript الرئيسي
│   │   ├── dashboard.js       # JavaScript لوحة التحكم
│   │   └── login.js           # JavaScript تسجيل الدخول
│   ├── fonts/
│   │   └── cairo/             # خطوط Cairo
│   ├── images/                # الصور والأيقونات
│   └── libs/                  # المكتبات الخارجية
│       ├── bootstrap/
│       ├── jquery/
│       ├── sweetalert2/
│       ├── toastr/
│       ├── chartjs/
│       └── fontawesome/
├── pages/                     # صفحات إضافية
├── index.html                 # لوحة التحكم الرئيسية
├── login.html                 # صفحة تسجيل الدخول
├── package.json               # ملف npm
├── setup-libraries.ps1        # سكريبت تثبيت المكتبات
├── LIBRARY_DOWNLOAD_GUIDE.md  # دليل تحميل المكتبات
└── README.md                  # هذا الملف
```

## 🚀 التثبيت والإعداد

### الطريقة 1: استخدام NPM (موصى بها)

1. **تثبيت Node.js** (إذا لم يكن مثبتاً)
   - قم بتحميل Node.js من: https://nodejs.org/

2. **تثبيت المكتبات**
   ```bash
   npm install
   ```

3. **نسخ المكتبات إلى مجلد assets**
   ```powershell
   .\setup-libraries.ps1
   ```

4. **تحميل خط Cairo**
   - قم بتحميل خط Cairo من: https://fonts.google.com/specimen/Cairo
   - ضع ملفات الخط في: `assets/fonts/cairo/`

### الطريقة 2: التحميل اليدوي

1. اتبع التعليمات في ملف `LIBRARY_DOWNLOAD_GUIDE.md`
2. قم بتحميل كل مكتبة ووضعها في المجلد المناسب
3. تأكد من تحميل خط Cairo ووضعه في `assets/fonts/cairo/`

## 🎯 الاستخدام

### فتح المشروع

1. افتح ملف `login.html` في المتصفح لعرض صفحة تسجيل الدخول
2. افتح ملف `index.html` في المتصفح لعرض لوحة التحكم

### بيانات تسجيل الدخول التجريبية

```
البريد الإلكتروني: <EMAIL>
كلمة المرور: 123456
```

### تشغيل على خادم محلي (اختياري)

يمكنك استخدام أي خادم ويب محلي مثل:

**باستخدام Python:**
```bash
python -m http.server 8000
```

**باستخدام PHP:**
```bash
php -S localhost:8000
```

**باستخدام Node.js (http-server):**
```bash
npx http-server -p 8000
```

ثم افتح المتصفح على: `http://localhost:8000`

## 📱 الصفحات المتوفرة

### 1. صفحة تسجيل الدخول (login.html)
- تصميم احترافي وجذاب
- نموذج تسجيل دخول مع التحقق من البيانات
- خيار "تذكرني"
- رابط استعادة كلمة المرور
- خيارات تسجيل الدخول الاجتماعي (Google, Microsoft)

### 2. لوحة التحكم الرئيسية (index.html)
- شريط جانبي قابل للطي
- شريط تنقل علوي مع بحث وإشعارات
- بطاقات إحصائيات تفاعلية
- رسوم بيانية للمبيعات والإيرادات
- جدول الطلبات الأخيرة
- قائمة النشاط الأخير

## 🎨 التخصيص

### تغيير الألوان

قم بتعديل ملف `assets/css/variables.css`:

```css
:root {
    --primary-color: #2563eb;      /* اللون الأساسي */
    --success-color: #10b981;      /* لون النجاح */
    --danger-color: #ef4444;       /* لون الخطر */
    --warning-color: #f59e0b;      /* لون التحذير */
    /* ... المزيد من المتغيرات */
}
```

### تغيير الخط

قم بتعديل ملف `assets/css/fonts.css` لإضافة خطوط جديدة.

### إضافة صفحات جديدة

1. أنشئ ملف HTML جديد في مجلد `pages/`
2. استخدم نفس هيكل `index.html` كقالب
3. قم بتضمين ملفات CSS و JS المطلوبة

## 🔧 المكونات الرئيسية

### الشريط الجانبي (Sidebar)
- قابل للطي والتوسيع
- قوائم فرعية قابلة للطي
- أيقونات ونصوص
- معلومات المستخدم في الأسفل

### شريط التنقل (Navbar)
- شريط بحث
- إشعارات منسدلة
- رسائل
- قائمة المستخدم المنسدلة

### بطاقات الإحصائيات (Stat Cards)
- عرض الأرقام والإحصائيات
- مؤشرات التغيير (زيادة/نقصان)
- أيقونات ملونة
- تأثيرات حركية

### الرسوم البيانية (Charts)
- رسم بياني خطي للمبيعات
- رسم بياني دائري للإيرادات
- دعم RTL كامل
- تسميات عربية

## 📊 الميزات التفاعلية

- ✅ تبديل الشريط الجانبي
- ✅ قوائم منسدلة تفاعلية
- ✅ إشعارات Toast
- ✅ نوافذ حوار SweetAlert2
- ✅ التحقق من نماذج الإدخال
- ✅ رسوم بيانية تفاعلية
- ✅ جداول قابلة للفرز
- ✅ بحث في الوقت الفعلي

## 🌐 دعم المتصفحات

- ✅ Chrome (آخر إصدارين)
- ✅ Firefox (آخر إصدارين)
- ✅ Safari (آخر إصدارين)
- ✅ Edge (آخر إصدارين)

## 📝 ملاحظات مهمة

1. **المكتبات المحلية**: تأكد من تحميل جميع المكتبات محلياً قبل الاستخدام
2. **خط Cairo**: يجب تحميل خط Cairo لعرض النصوص العربية بشكل صحيح
3. **Bootstrap RTL**: استخدم ملف `bootstrap.rtl.min.css` وليس `bootstrap.min.css`
4. **الصور**: استبدل الصور الافتراضية بصورك الخاصة في مجلد `assets/images/`

## 🤝 المساهمة

هذا قالب مفتوح المصدر. يمكنك:
- تخصيصه حسب احتياجاتك
- إضافة ميزات جديدة
- تحسين التصميم
- إصلاح الأخطاء

## 📄 الترخيص

MIT License - يمكنك استخدام هذا القالب في مشاريعك التجارية والشخصية.

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف `LIBRARY_DOWNLOAD_GUIDE.md`
2. تأكد من تحميل جميع المكتبات بشكل صحيح
3. افتح console المتصفح للتحقق من الأخطاء

## 🎉 شكراً لاستخدامك هذا القالب!

نتمنى أن يساعدك هذا القالب في بناء نظام ERP احترافي وفعال.

---

**تم التطوير بـ ❤️ للمجتمع العربي**

</div>

