/**
 * ERP Dashboard - Login Page JavaScript
 * جافاسكريبت صفحة تسجيل الدخول
 */

(function($) {
    'use strict';

    // ==================== DOM Elements ====================
    const loginForm = $('#loginForm');
    const emailInput = $('#email');
    const passwordInput = $('#password');
    const rememberMeCheckbox = $('#rememberMe');
    const loginBtn = $('#loginBtn');
    const loginBtnText = $('#loginBtnText');
    const togglePasswordBtn = $('#togglePassword');
    const forgotPasswordLink = $('#forgotPasswordLink');
    const registerLink = $('#registerLink');
    const loginAlert = $('#loginAlert');
    const loginAlertMessage = $('#loginAlertMessage');

    // Social login buttons
    const googleLoginBtn = $('#googleLogin');
    const microsoftLoginBtn = $('#microsoftLogin');

    // ==================== Password Toggle ====================
    togglePasswordBtn.on('click', function() {
        const type = passwordInput.attr('type') === 'password' ? 'text' : 'password';
        passwordInput.attr('type', type);
        
        // Toggle icon
        const icon = $(this).find('i');
        icon.toggleClass('fa-eye fa-eye-slash');
    });

    // ==================== Form Validation ====================
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    function validatePassword(password) {
        return password.length >= 6;
    }

    function showFieldError(field, message) {
        field.addClass('is-invalid');
        field.next('.invalid-feedback').text(message).show();
    }

    function clearFieldError(field) {
        field.removeClass('is-invalid');
        field.next('.invalid-feedback').text('').hide();
    }

    function showAlert(message, type = 'error') {
        loginAlert.removeClass('login-alert-error login-alert-success');
        loginAlert.addClass('login-alert-' + type);
        loginAlertMessage.text(message);
        loginAlert.fadeIn();
        
        // Auto hide after 5 seconds
        setTimeout(function() {
            loginAlert.fadeOut();
        }, 5000);
    }

    function hideAlert() {
        loginAlert.fadeOut();
    }

    // Clear errors on input
    emailInput.on('input', function() {
        clearFieldError($(this));
        hideAlert();
    });

    passwordInput.on('input', function() {
        clearFieldError($(this));
        hideAlert();
    });

    // ==================== Form Submission ====================
    loginForm.on('submit', function(e) {
        e.preventDefault();
        
        // Clear previous errors
        clearFieldError(emailInput);
        clearFieldError(passwordInput);
        hideAlert();
        
        // Get form values
        const email = emailInput.val().trim();
        const password = passwordInput.val();
        const rememberMe = rememberMeCheckbox.is(':checked');
        
        // Validate
        let isValid = true;
        
        if (!email) {
            showFieldError(emailInput, 'البريد الإلكتروني مطلوب');
            isValid = false;
        } else if (!validateEmail(email)) {
            showFieldError(emailInput, 'البريد الإلكتروني غير صحيح');
            isValid = false;
        }
        
        if (!password) {
            showFieldError(passwordInput, 'كلمة المرور مطلوبة');
            isValid = false;
        } else if (!validatePassword(password)) {
            showFieldError(passwordInput, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            isValid = false;
        }
        
        if (!isValid) {
            return;
        }
        
        // Show loading state
        loginBtn.prop('disabled', true);
        loginBtnText.text('جاري تسجيل الدخول...');
        loginBtn.append('<span class="spinner"></span>');
        
        // Simulate API call
        setTimeout(function() {
            // Demo credentials
            if (email === '<EMAIL>' && password === '123456') {
                // Success
                if (rememberMe) {
                    localStorage.setItem('rememberMe', 'true');
                    localStorage.setItem('userEmail', email);
                }
                
                // Show success message
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم تسجيل الدخول بنجاح!',
                        text: 'جاري تحويلك إلى لوحة التحكم...',
                        timer: 2000,
                        showConfirmButton: false
                    }).then(function() {
                        window.location.href = 'index.html';
                    });
                } else {
                    showAlert('تم تسجيل الدخول بنجاح!', 'success');
                    setTimeout(function() {
                        window.location.href = 'index.html';
                    }, 1500);
                }
            } else {
                // Error
                loginBtn.prop('disabled', false);
                loginBtnText.text('تسجيل الدخول');
                loginBtn.find('.spinner').remove();
                
                showAlert('البريد الإلكتروني أو كلمة المرور غير صحيحة');
                
                // Shake animation
                loginForm.addClass('shake');
                setTimeout(function() {
                    loginForm.removeClass('shake');
                }, 500);
            }
        }, 1500);
    });

    // ==================== Forgot Password ====================
    forgotPasswordLink.on('click', function(e) {
        e.preventDefault();
        
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'استعادة كلمة المرور',
                html: '<input id="swal-email" class="form-control" placeholder="أدخل بريدك الإلكتروني" type="email">',
                showCancelButton: true,
                confirmButtonText: 'إرسال',
                cancelButtonText: 'إلغاء',
                reverseButtons: true,
                preConfirm: function() {
                    const email = $('#swal-email').val();
                    if (!email) {
                        Swal.showValidationMessage('البريد الإلكتروني مطلوب');
                        return false;
                    }
                    if (!validateEmail(email)) {
                        Swal.showValidationMessage('البريد الإلكتروني غير صحيح');
                        return false;
                    }
                    return email;
                }
            }).then(function(result) {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم الإرسال!',
                        text: 'تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني',
                        confirmButtonText: 'حسناً'
                    });
                }
            });
        } else {
            const email = prompt('أدخل بريدك الإلكتروني:');
            if (email && validateEmail(email)) {
                alert('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني');
            }
        }
    });

    // ==================== Register Link ====================
    registerLink.on('click', function(e) {
        e.preventDefault();
        
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'info',
                title: 'إنشاء حساب جديد',
                text: 'يرجى التواصل مع مدير النظام لإنشاء حساب جديد',
                confirmButtonText: 'حسناً'
            });
        } else {
            alert('يرجى التواصل مع مدير النظام لإنشاء حساب جديد');
        }
    });

    // ==================== Social Login ====================
    googleLoginBtn.on('click', function() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'info',
                title: 'تسجيل الدخول بواسطة Google',
                text: 'هذه الميزة قيد التطوير',
                confirmButtonText: 'حسناً'
            });
        }
    });

    microsoftLoginBtn.on('click', function() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'info',
                title: 'تسجيل الدخول بواسطة Microsoft',
                text: 'هذه الميزة قيد التطوير',
                confirmButtonText: 'حسناً'
            });
        }
    });

    // ==================== Remember Me ====================
    function checkRememberMe() {
        const rememberMe = localStorage.getItem('rememberMe');
        const savedEmail = localStorage.getItem('userEmail');
        
        if (rememberMe === 'true' && savedEmail) {
            emailInput.val(savedEmail);
            rememberMeCheckbox.prop('checked', true);
        }
    }

    // ==================== Keyboard Shortcuts ====================
    $(document).on('keydown', function(e) {
        // Enter key to submit
        if (e.key === 'Enter' && !loginForm.is(':focus')) {
            loginForm.submit();
        }
    });

    // ==================== Initialize ====================
    function init() {
        // Check remember me
        checkRememberMe();
        
        // Focus on email input
        emailInput.focus();
        
        // Add shake animation CSS
        if (!$('#shake-animation').length) {
            $('<style id="shake-animation">')
                .text('@keyframes shake { 0%, 100% { transform: translateX(0); } 10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); } 20%, 40%, 60%, 80% { transform: translateX(10px); } } .shake { animation: shake 0.5s; }')
                .appendTo('head');
        }
    }

    // ==================== Document Ready ====================
    $(document).ready(function() {
        init();
        
        // Show demo credentials info
        console.log('%c=== معلومات تسجيل الدخول التجريبية ===', 'color: #2563eb; font-size: 16px; font-weight: bold;');
        console.log('%cالبريد الإلكتروني: <EMAIL>', 'color: #10b981; font-size: 14px;');
        console.log('%cكلمة المرور: 123456', 'color: #10b981; font-size: 14px;');
    });

})(jQuery);

