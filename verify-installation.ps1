# PowerShell Script to Verify Installation
# سكريبت للتحقق من التثبيت

Write-Host "==================================" -ForegroundColor Cyan
Write-Host "ERP Dashboard - Installation Verification" -ForegroundColor Cyan
Write-Host "التحقق من تثبيت نظام ERP" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan
Write-Host ""

$allGood = $true
$missingFiles = @()

# Function to check file existence
function Test-FileExists {
    param($path, $description)
    
    if (Test-Path $path) {
        Write-Host "✓ $description" -ForegroundColor Green
        return $true
    } else {
        Write-Host "✗ $description" -ForegroundColor Red
        $script:allGood = $false
        $script:missingFiles += $path
        return $false
    }
}

Write-Host "Checking required files..." -ForegroundColor Yellow
Write-Host "جاري التحقق من الملفات المطلوبة..." -ForegroundColor Yellow
Write-Host ""

# Check Bootstrap
Write-Host "[1/10] Bootstrap..." -ForegroundColor Cyan
Test-FileExists "assets/libs/bootstrap/css/bootstrap.rtl.min.css" "Bootstrap RTL CSS"
Test-FileExists "assets/libs/bootstrap/js/bootstrap.bundle.min.js" "Bootstrap JS"
Write-Host ""

# Check jQuery
Write-Host "[2/10] jQuery..." -ForegroundColor Cyan
Test-FileExists "assets/libs/jquery/jquery.min.js" "jQuery"
Write-Host ""

# Check SweetAlert2
Write-Host "[3/10] SweetAlert2..." -ForegroundColor Cyan
Test-FileExists "assets/libs/sweetalert2/sweetalert2.min.css" "SweetAlert2 CSS"
Test-FileExists "assets/libs/sweetalert2/sweetalert2.min.js" "SweetAlert2 JS"
Write-Host ""

# Check Toastr
Write-Host "[4/10] Toastr..." -ForegroundColor Cyan
Test-FileExists "assets/libs/toastr/toastr.min.css" "Toastr CSS"
Test-FileExists "assets/libs/toastr/toastr.min.js" "Toastr JS"
Write-Host ""

# Check Chart.js
Write-Host "[5/10] Chart.js..." -ForegroundColor Cyan
Test-FileExists "assets/libs/chartjs/chart.min.js" "Chart.js"
Write-Host ""

# Check Font Awesome
Write-Host "[6/10] Font Awesome..." -ForegroundColor Cyan
Test-FileExists "assets/libs/fontawesome/css/all.min.css" "Font Awesome CSS"
Write-Host ""

# Check Cairo Font
Write-Host "[7/10] Cairo Font..." -ForegroundColor Cyan
Test-FileExists "assets/fonts/cairo/Cairo-Regular.ttf" "Cairo Regular"
Test-FileExists "assets/fonts/cairo/Cairo-Bold.ttf" "Cairo Bold"
Write-Host ""

# Check CSS Files
Write-Host "[8/10] Custom CSS Files..." -ForegroundColor Cyan
Test-FileExists "assets/css/variables.css" "Variables CSS"
Test-FileExists "assets/css/fonts.css" "Fonts CSS"
Test-FileExists "assets/css/main.css" "Main CSS"
Test-FileExists "assets/css/sidebar.css" "Sidebar CSS"
Test-FileExists "assets/css/navbar.css" "Navbar CSS"
Test-FileExists "assets/css/dashboard.css" "Dashboard CSS"
Test-FileExists "assets/css/login.css" "Login CSS"
Write-Host ""

# Check JS Files
Write-Host "[9/10] Custom JavaScript Files..." -ForegroundColor Cyan
Test-FileExists "assets/js/main.js" "Main JS"
Test-FileExists "assets/js/dashboard.js" "Dashboard JS"
Test-FileExists "assets/js/login.js" "Login JS"
Write-Host ""

# Check HTML Files
Write-Host "[10/10] HTML Files..." -ForegroundColor Cyan
Test-FileExists "index.html" "Dashboard Page"
Test-FileExists "login.html" "Login Page"
Write-Host ""

# Summary
Write-Host "==================================" -ForegroundColor Cyan
Write-Host "Verification Summary" -ForegroundColor Cyan
Write-Host "ملخص التحقق" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan
Write-Host ""

if ($allGood) {
    Write-Host "✓ All files are present!" -ForegroundColor Green
    Write-Host "✓ جميع الملفات موجودة!" -ForegroundColor Green
    Write-Host ""
    Write-Host "You can now open login.html in your browser." -ForegroundColor Green
    Write-Host "يمكنك الآن فتح login.html في المتصفح." -ForegroundColor Green
    Write-Host ""
    Write-Host "Demo credentials:" -ForegroundColor Yellow
    Write-Host "Email: <EMAIL>" -ForegroundColor White
    Write-Host "Password: 123456" -ForegroundColor White
} else {
    Write-Host "✗ Some files are missing!" -ForegroundColor Red
    Write-Host "✗ بعض الملفات مفقودة!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Missing files:" -ForegroundColor Yellow
    Write-Host "الملفات المفقودة:" -ForegroundColor Yellow
    foreach ($file in $missingFiles) {
        Write-Host "  - $file" -ForegroundColor Red
    }
    Write-Host ""
    Write-Host "Please follow the instructions in LIBRARY_DOWNLOAD_GUIDE.md" -ForegroundColor Yellow
    Write-Host "يرجى اتباع التعليمات في ملف LIBRARY_DOWNLOAD_GUIDE.md" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Quick fix:" -ForegroundColor Cyan
    Write-Host "1. Run: npm install" -ForegroundColor White
    Write-Host "2. Run: .\setup-libraries.ps1" -ForegroundColor White
    Write-Host "3. Download Cairo font manually" -ForegroundColor White
}

Write-Host ""
Write-Host "==================================" -ForegroundColor Cyan
Write-Host ""

