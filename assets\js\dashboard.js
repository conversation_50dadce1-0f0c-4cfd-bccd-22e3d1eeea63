/**
 * ERP Dashboard - Dashboard Page JavaScript
 * جافاسكريبت صفحة لوحة التحكم
 */

(function($) {
    'use strict';

    // ==================== Chart.js Configuration ====================
    if (typeof Chart !== 'undefined') {
        // Set default font family and colors
        Chart.defaults.font.family = 'Cairo, sans-serif';
        Chart.defaults.color = '#64748b';
        
        // RTL support for Chart.js
        Chart.defaults.plugins.legend.rtl = true;
        Chart.defaults.plugins.legend.textDirection = 'rtl';
    }

    // ==================== Sales Chart ====================
    function initSalesChart() {
        const ctx = document.getElementById('salesChart');
        if (!ctx || typeof Chart === 'undefined') return;

        const salesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                datasets: [
                    {
                        label: 'المبيعات',
                        data: [12000, 19000, 15000, 25000, 22000, 30000, 28000],
                        borderColor: '#2563eb',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 5,
                        pointHoverRadius: 7,
                        pointBackgroundColor: '#2563eb',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2
                    },
                    {
                        label: 'الطلبات',
                        data: [8000, 12000, 10000, 18000, 16000, 22000, 20000],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 5,
                        pointHoverRadius: 7,
                        pointBackgroundColor: '#10b981',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        align: 'end',
                        rtl: true,
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                size: 13,
                                family: 'Cairo, sans-serif'
                            }
                        }
                    },
                    tooltip: {
                        rtl: true,
                        textDirection: 'rtl',
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        padding: 12,
                        cornerRadius: 8,
                        titleFont: {
                            size: 14,
                            family: 'Cairo, sans-serif'
                        },
                        bodyFont: {
                            size: 13,
                            family: 'Cairo, sans-serif'
                        },
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += new Intl.NumberFormat('ar-SA').format(context.parsed.y) + ' ر.س';
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                size: 12,
                                family: 'Cairo, sans-serif'
                            },
                            callback: function(value) {
                                return new Intl.NumberFormat('ar-SA').format(value);
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false,
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                size: 12,
                                family: 'Cairo, sans-serif'
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });

        return salesChart;
    }

    // ==================== Revenue Distribution Chart ====================
    function initRevenueChart() {
        const ctx = document.getElementById('revenueChart');
        if (!ctx || typeof Chart === 'undefined') return;

        const revenueChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['المنتجات', 'الخدمات', 'الاشتراكات', 'أخرى'],
                datasets: [{
                    data: [45, 25, 20, 10],
                    backgroundColor: [
                        '#2563eb',
                        '#10b981',
                        '#f59e0b',
                        '#8b5cf6'
                    ],
                    borderWidth: 0,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        rtl: true,
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            font: {
                                size: 13,
                                family: 'Cairo, sans-serif'
                            }
                        }
                    },
                    tooltip: {
                        rtl: true,
                        textDirection: 'rtl',
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        padding: 12,
                        cornerRadius: 8,
                        titleFont: {
                            size: 14,
                            family: 'Cairo, sans-serif'
                        },
                        bodyFont: {
                            size: 13,
                            family: 'Cairo, sans-serif'
                        },
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                return label + ': ' + value + '%';
                            }
                        }
                    }
                },
                cutout: '70%'
            }
        });

        return revenueChart;
    }

    // ==================== Animate Statistics ====================
    function animateStatistics() {
        $('.stat-card-value').each(function() {
            const $this = $(this);
            const text = $this.text();
            const number = parseInt(text.replace(/[^0-9]/g, ''));
            
            if (!isNaN(number)) {
                $this.prop('Counter', 0).animate({
                    Counter: number
                }, {
                    duration: 2000,
                    easing: 'swing',
                    step: function(now) {
                        const formatted = new Intl.NumberFormat('ar-SA').format(Math.ceil(now));
                        const suffix = text.replace(/[0-9,]/g, '');
                        $this.text(formatted + suffix);
                    }
                });
            }
        });
    }

    // ==================== Quick Actions ====================
    $('.quick-action-btn').on('click', function(e) {
        e.preventDefault();
        const action = $(this).find('.quick-action-text').text();
        
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'info',
                title: action,
                text: 'هذه الميزة قيد التطوير',
                confirmButtonText: 'حسناً'
            });
        }
    });

    // ==================== Table Actions ====================
    $('.table .btn').on('click', function(e) {
        e.preventDefault();
        const row = $(this).closest('tr');
        const orderId = row.find('td:first').text();
        
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'تفاصيل الطلب',
                html: '<p>رقم الطلب: <strong>' + orderId + '</strong></p><p>هذه الميزة قيد التطوير</p>',
                confirmButtonText: 'حسناً'
            });
        }
    });

    // ==================== Refresh Data ====================
    function refreshDashboardData() {
        // Simulate data refresh
        if (typeof toastr !== 'undefined') {
            toastr.info('جاري تحديث البيانات...', 'تحديث');
        }
        
        setTimeout(function() {
            if (typeof toastr !== 'undefined') {
                toastr.success('تم تحديث البيانات بنجاح', 'نجح!');
            }
        }, 1500);
    }

    // Auto refresh every 5 minutes
    setInterval(refreshDashboardData, 300000);

    // ==================== Initialize ====================
    function init() {
        // Initialize charts
        const salesChart = initSalesChart();
        const revenueChart = initRevenueChart();
        
        // Animate statistics
        setTimeout(animateStatistics, 500);
        
        // Store charts globally for potential updates
        window.dashboardCharts = {
            sales: salesChart,
            revenue: revenueChart
        };
    }

    // ==================== Document Ready ====================
    $(document).ready(function() {
        init();
    });

    // ==================== Window Resize ====================
    $(window).on('resize', function() {
        // Charts will auto-resize due to responsive: true option
    });

})(jQuery);

