# PowerShell Script to Copy Libraries from node_modules
# سكريبت لنسخ المكتبات من node_modules

Write-Host "==================================" -ForegroundColor Cyan
Write-Host "ERP Dashboard - Library Setup" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan
Write-Host ""

# Check if node_modules exists
if (-Not (Test-Path "node_modules")) {
    Write-Host "Error: node_modules folder not found!" -ForegroundColor Red
    Write-Host "Please run: npm install" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Or install packages manually:" -ForegroundColor Yellow
    Write-Host "npm install bootstrap@5.3.2 jquery@3.7.1 sweetalert2 toastr chart.js @fortawesome/fontawesome-free" -ForegroundColor White
    exit 1
}

Write-Host "Copying libraries..." -ForegroundColor Green
Write-Host ""

# Bootstrap
Write-Host "[1/6] Copying Bootstrap..." -ForegroundColor Yellow
Copy-Item "node_modules/bootstrap/dist/css/*" "assets/libs/bootstrap/css/" -Recurse -Force
Copy-Item "node_modules/bootstrap/dist/js/bootstrap.bundle.min.js*" "assets/libs/bootstrap/js/" -Force
Write-Host "✓ Bootstrap copied successfully" -ForegroundColor Green

# jQuery
Write-Host "[2/6] Copying jQuery..." -ForegroundColor Yellow
Copy-Item "node_modules/jquery/dist/jquery.min.js" "assets/libs/jquery/" -Force
Write-Host "✓ jQuery copied successfully" -ForegroundColor Green

# SweetAlert2
Write-Host "[3/6] Copying SweetAlert2..." -ForegroundColor Yellow
Copy-Item "node_modules/sweetalert2/dist/sweetalert2.min.css" "assets/libs/sweetalert2/" -Force
Copy-Item "node_modules/sweetalert2/dist/sweetalert2.min.js" "assets/libs/sweetalert2/" -Force
Write-Host "✓ SweetAlert2 copied successfully" -ForegroundColor Green

# Toastr
Write-Host "[4/6] Copying Toastr..." -ForegroundColor Yellow
Copy-Item "node_modules/toastr/build/toastr.min.css" "assets/libs/toastr/" -Force
Copy-Item "node_modules/toastr/build/toastr.min.js" "assets/libs/toastr/" -Force
Write-Host "✓ Toastr copied successfully" -ForegroundColor Green

# Chart.js
Write-Host "[5/6] Copying Chart.js..." -ForegroundColor Yellow
Copy-Item "node_modules/chart.js/dist/chart.umd.js" "assets/libs/chartjs/chart.min.js" -Force
Write-Host "✓ Chart.js copied successfully" -ForegroundColor Green

# Font Awesome
Write-Host "[6/6] Copying Font Awesome..." -ForegroundColor Yellow
Copy-Item "node_modules/@fortawesome/fontawesome-free/css/*" "assets/libs/fontawesome/css/" -Recurse -Force
Copy-Item "node_modules/@fortawesome/fontawesome-free/webfonts/*" "assets/libs/fontawesome/webfonts/" -Recurse -Force
Write-Host "✓ Font Awesome copied successfully" -ForegroundColor Green

Write-Host ""
Write-Host "==================================" -ForegroundColor Cyan
Write-Host "All libraries copied successfully!" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Note: You still need to download Cairo font manually" -ForegroundColor Yellow
Write-Host "Download from: https://fonts.google.com/specimen/Cairo" -ForegroundColor White
Write-Host "Place font files in: assets/fonts/cairo/" -ForegroundColor White
Write-Host ""

