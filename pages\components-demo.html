<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض المكونات - نظام ERP</title>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="../assets/libs/bootstrap/css/bootstrap.rtl.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="../assets/libs/fontawesome/css/all.min.css">
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="../assets/libs/sweetalert2/sweetalert2.min.css">
    
    <!-- Toastr -->
    <link rel="stylesheet" href="../assets/libs/toastr/toastr.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/variables.css">
    <link rel="stylesheet" href="../assets/css/fonts.css">
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/sidebar.css">
    <link rel="stylesheet" href="../assets/css/navbar.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    
    <style>
        .demo-section {
            margin-bottom: 3rem;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .demo-section h2 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary-color);
        }
        
        .demo-section h3 {
            color: var(--text-primary);
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-size: 1.25rem;
        }
        
        .demo-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .color-box {
            width: 100px;
            height: 100px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin: 0.5rem;
        }
        
        .icon-demo {
            font-size: 2rem;
            margin: 1rem;
            color: var(--primary-color);
        }
        
        .chart-demo-container {
            height: 300px;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <!-- Wrapper -->
    <div class="wrapper">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <!-- Sidebar Toggle Button -->
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-chevron-left"></i>
            </button>

            <!-- Sidebar Brand -->
            <div class="sidebar-brand">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="sidebar-brand-text">نظام ERP</div>
            </div>

            <!-- Sidebar Navigation -->
            <nav class="sidebar-nav">
                <!-- Main Section -->
                <div class="nav-section">
                    <div class="nav-section-title">القائمة الرئيسية</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="index.html" class="nav-link active">
                                <i class="nav-link-icon fas fa-home"></i>
                                <span class="nav-link-text">لوحة التحكم</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-home"></i>
                                        <span>لوحة التحكم</span>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-chart-line"></i>
                                <span class="nav-link-text">التقارير</span>
                                <span class="badge badge-primary nav-link-badge">5</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-chart-line"></i>
                                        <span>التقارير</span>
                                        <span class="badge bg-primary" style="margin-right: auto;">5</span>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-bell"></i>
                                <span class="nav-link-text">الإشعارات</span>
                                <span class="badge badge-danger nav-link-badge">12</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-bell"></i>
                                        <span>الإشعارات</span>
                                        <span class="badge bg-danger" style="margin-right: auto;">12</span>
                                    </div>
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Management Section -->
                <div class="nav-section">
                    <div class="nav-section-title">الإدارة</div>
                    <ul class="nav-list">
                        <li class="nav-item has-submenu">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-users"></i>
                                <span class="nav-link-text">إدارة الموظفين</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-users"></i>
                                        <span>إدارة الموظفين</span>
                                    </div>
                                    <ul class="nav-popup-submenu">
                                        <li>
                                            <a href="#">
                                                <i class="fas fa-list"></i>
                                                <span>قائمة الموظفين</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#">
                                                <i class="fas fa-user-plus"></i>
                                                <span>إضافة موظف</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#">
                                                <i class="fas fa-clock"></i>
                                                <span>الحضور والانصراف</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </a>
                            <ul class="submenu">
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <span class="nav-link-text">قائمة الموظفين</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <span class="nav-link-text">إضافة موظف</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <span class="nav-link-text">الحضور والانصراف</span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="nav-item has-submenu">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-box"></i>
                                <span class="nav-link-text">إدارة المخزون</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-box"></i>
                                        <span>إدارة المخزون</span>
                                    </div>
                                    <ul class="nav-popup-submenu">
                                        <li>
                                            <a href="#">
                                                <i class="fas fa-boxes"></i>
                                                <span>المنتجات</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#">
                                                <i class="fas fa-warehouse"></i>
                                                <span>المخازن</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </a>
                            <ul class="submenu">
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <span class="nav-link-text">المنتجات</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <span class="nav-link-text">المخازن</span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-wallet"></i>
                                <span class="nav-link-text">المالية</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-wallet"></i>
                                        <span>المالية</span>
                                    </div>
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Pages Section -->
                <div class="nav-section">
                    <div class="nav-section-title">الصفحات</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="pos.html" class="nav-link">
                                <i class="nav-link-icon fas fa-cash-register"></i>
                                <span class="nav-link-text">نقطة البيع POS</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-cash-register"></i>
                                        <span>نقطة البيع POS</span>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="products.html" class="nav-link">
                                <i class="nav-link-icon fas fa-box"></i>
                                <span class="nav-link-text">المنتجات</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-box"></i>
                                        <span>المنتجات</span>
                                    </div>
                                </div>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="invoices.html" class="nav-link">
                                <i class="nav-link-icon fas fa-file-invoice"></i>
                                <span class="nav-link-text">الفواتير</span>
                                <!-- Popup for collapsed sidebar -->
                                <div class="nav-popup">
                                    <div class="nav-popup-header">
                                        <i class="fas fa-file-invoice"></i>
                                        <span>الفواتير</span>
                                    </div>
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Demo Pages Section -->
                <div class="nav-section">
                    <div class="nav-section-title">صفحات العرض</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="components-demo.html" class="nav-link">
                                <i class="nav-link-icon fas fa-puzzle-piece"></i>
                                <span class="nav-link-text">عرض المكونات</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="jquery-demo.html" class="nav-link">
                                <i class="nav-link-icon fab fa-js"></i>
                                <span class="nav-link-text">عرض jQuery</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="sample-page.html" class="nav-link">
                                <i class="nav-link-icon fas fa-file"></i>
                                <span class="nav-link-text">صفحة نموذجية</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Settings Section -->
                <div class="nav-section">
                    <div class="nav-section-title">الإعدادات</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-cog"></i>
                                <span class="nav-link-text">إعدادات النظام</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-user-shield"></i>
                                <span class="nav-link-text">الصلاحيات</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

        </aside>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Navbar -->
            <nav class="navbar">
                <div class="navbar-start">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title">عرض جميع المكونات والمكتبات</h1>
                </div>

                <div class="navbar-end">
                    <div class="navbar-item">
                        <button class="icon-button" id="notificationsBtn">
                            <i class="fas fa-bell"></i>
                            <span class="badge">5</span>
                        </button>
                    </div>

                    <div class="navbar-item navbar-user">
                        <button class="user-button" id="userMenuBtn">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <span class="user-name">مدير النظام</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="navbar-dropdown" id="userMenu">
                            <a href="#" class="dropdown-item">
                                <i class="fas fa-user"></i>
                                <span>الملف الشخصي</span>
                            </a>
                            <a href="#" class="dropdown-item">
                                <i class="fas fa-cog"></i>
                                <span>الإعدادات</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="../login.html" class="dropdown-item">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Content -->
            <div class="content-wrapper">
                <div class="container-fluid">
                    
                    <!-- Bootstrap Buttons -->
                    <div class="demo-section">
                        <h2><i class="fab fa-bootstrap"></i> Bootstrap - الأزرار</h2>
                        
                        <h3>الأزرار الأساسية</h3>
                        <div class="demo-buttons">
                            <button class="btn btn-primary">أساسي</button>
                            <button class="btn btn-secondary">ثانوي</button>
                            <button class="btn btn-success">نجاح</button>
                            <button class="btn btn-danger">خطر</button>
                            <button class="btn btn-warning">تحذير</button>
                            <button class="btn btn-info">معلومة</button>
                            <button class="btn btn-light">فاتح</button>
                            <button class="btn btn-dark">داكن</button>
                        </div>
                        
                        <h3>أزرار محددة</h3>
                        <div class="demo-buttons">
                            <button class="btn btn-outline-primary">أساسي</button>
                            <button class="btn btn-outline-success">نجاح</button>
                            <button class="btn btn-outline-danger">خطر</button>
                            <button class="btn btn-outline-warning">تحذير</button>
                        </div>
                        
                        <h3>أحجام الأزرار</h3>
                        <div class="demo-buttons">
                            <button class="btn btn-primary btn-lg">كبير</button>
                            <button class="btn btn-primary">عادي</button>
                            <button class="btn btn-primary btn-sm">صغير</button>
                        </div>
                        
                        <h3>أزرار مع أيقونات</h3>
                        <div class="demo-buttons">
                            <button class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ
                            </button>
                            <button class="btn btn-success">
                                <i class="fas fa-check"></i> تأكيد
                            </button>
                            <button class="btn btn-danger">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                            <button class="btn btn-info">
                                <i class="fas fa-download"></i> تحميل
                            </button>
                        </div>
                    </div>

                    <!-- Bootstrap Modals -->
                    <div class="demo-section">
                        <h2><i class="fab fa-bootstrap"></i> Bootstrap - النوافذ المنبثقة (Modals)</h2>

                        <h3>أنواع النوافذ المنبثقة</h3>
                        <div class="demo-buttons">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#basicModal">
                                <i class="fas fa-window-maximize"></i> نافذة أساسية
                            </button>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#largeModal">
                                <i class="fas fa-expand"></i> نافذة كبيرة
                            </button>
                            <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#smallModal">
                                <i class="fas fa-compress"></i> نافذة صغيرة
                            </button>
                            <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#xlModal">
                                <i class="fas fa-arrows-alt"></i> نافذة كبيرة جداً
                            </button>
                            <button class="btn btn-secondary" data-bs-toggle="modal" data-bs-target="#fullscreenModal">
                                <i class="fas fa-desktop"></i> نافذة ملء الشاشة
                            </button>
                        </div>

                        <h3>نوافذ قابلة للتمرير</h3>
                        <div class="demo-buttons">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#scrollModal">
                                <i class="fas fa-scroll"></i> نافذة قابلة للتمرير
                            </button>
                            <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#scrollableBodyModal">
                                <i class="fas fa-arrows-alt-v"></i> تمرير المحتوى فقط
                            </button>
                        </div>

                        <h3>نوافذ متمركزة</h3>
                        <div class="demo-buttons">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#centeredModal">
                                <i class="fas fa-align-center"></i> نافذة متمركزة
                            </button>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#centeredScrollModal">
                                <i class="fas fa-align-center"></i> متمركزة + قابلة للتمرير
                            </button>
                        </div>

                        <h3>نوافذ مع حركات (Animations)</h3>
                        <div class="demo-buttons">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#fadeModal">
                                <i class="fas fa-magic"></i> Fade (افتراضي)
                            </button>
                            <button class="btn btn-success" onclick="showModalWithAnimation('slideModal', 'slide')">
                                <i class="fas fa-arrow-down"></i> Slide من الأعلى
                            </button>
                            <button class="btn btn-info" onclick="showModalWithAnimation('zoomModal', 'zoom')">
                                <i class="fas fa-search-plus"></i> Zoom
                            </button>
                            <button class="btn btn-warning" onclick="showModalWithAnimation('flipModal', 'flip')">
                                <i class="fas fa-sync"></i> Flip
                            </button>
                            <button class="btn btn-danger" onclick="showModalWithAnimation('bounceModal', 'bounce')">
                                <i class="fas fa-basketball-ball"></i> Bounce
                            </button>
                        </div>

                        <h3>نوافذ بدون خلفية (Backdrop)</h3>
                        <div class="demo-buttons">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#noBackdropModal">
                                <i class="fas fa-ban"></i> بدون خلفية
                            </button>
                            <button class="btn btn-secondary" data-bs-toggle="modal" data-bs-target="#staticBackdropModal">
                                <i class="fas fa-lock"></i> خلفية ثابتة (لا تغلق)
                            </button>
                        </div>

                        <h3>نوافذ مع نماذج</h3>
                        <div class="demo-buttons">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#formModal">
                                <i class="fas fa-edit"></i> نافذة مع نموذج
                            </button>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#loginModal">
                                <i class="fas fa-sign-in-alt"></i> نموذج تسجيل دخول
                            </button>
                        </div>

                        <h3>نوافذ متعددة (Nested)</h3>
                        <div class="demo-buttons">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#firstModal">
                                <i class="fas fa-layer-group"></i> فتح نافذة متعددة
                            </button>
                        </div>

                        <h3>نوافذ ملونة</h3>
                        <div class="demo-buttons">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#primaryModal">
                                <i class="fas fa-palette"></i> أزرق
                            </button>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#successModal">
                                <i class="fas fa-check-circle"></i> أخضر
                            </button>
                            <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#dangerModal">
                                <i class="fas fa-exclamation-circle"></i> أحمر
                            </button>
                            <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#warningModal">
                                <i class="fas fa-exclamation-triangle"></i> أصفر
                            </button>
                            <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#infoModal">
                                <i class="fas fa-info-circle"></i> سماوي
                            </button>
                        </div>
                    </div>

                    <!-- Bootstrap Alerts -->
                    <div class="demo-section">
                        <h2><i class="fab fa-bootstrap"></i> Bootstrap - التنبيهات (Alerts)</h2>
                        
                        <div class="alert alert-primary" role="alert">
                            <i class="fas fa-info-circle"></i> هذا تنبيه أساسي - تحقق منه!
                        </div>
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle"></i> تم! تم إكمال العملية بنجاح.
                        </div>
                        <div class="alert alert-warning" role="alert">
                            <i class="fas fa-exclamation-triangle"></i> تحذير! يجب الانتباه لهذا الأمر.
                        </div>
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-times-circle"></i> خطأ! حدث خطأ ما.
                        </div>
                    </div>

                    <!-- SweetAlert2 -->
                    <div class="demo-section">
                        <h2><i class="fas fa-window-maximize"></i> SweetAlert2 - نوافذ حوار جميلة</h2>
                        
                        <div class="demo-buttons">
                            <button class="btn btn-primary" onclick="showBasicAlert()">
                                <i class="fas fa-info-circle"></i> تنبيه أساسي
                            </button>
                            <button class="btn btn-success" onclick="showSuccessAlert()">
                                <i class="fas fa-check-circle"></i> نجاح
                            </button>
                            <button class="btn btn-danger" onclick="showErrorAlert()">
                                <i class="fas fa-times-circle"></i> خطأ
                            </button>
                            <button class="btn btn-warning" onclick="showWarningAlert()">
                                <i class="fas fa-exclamation-triangle"></i> تحذير
                            </button>
                            <button class="btn btn-info" onclick="showQuestionAlert()">
                                <i class="fas fa-question-circle"></i> سؤال
                            </button>
                            <button class="btn btn-secondary" onclick="showConfirmAlert()">
                                <i class="fas fa-check-double"></i> تأكيد
                            </button>
                            <button class="btn btn-dark" onclick="showInputAlert()">
                                <i class="fas fa-keyboard"></i> إدخال نص
                            </button>
                            <button class="btn btn-primary" onclick="showTimerAlert()">
                                <i class="fas fa-clock"></i> مع مؤقت
                            </button>
                        </div>
                    </div>

                    <!-- Toastr -->
                    <div class="demo-section">
                        <h2><i class="fas fa-comment-dots"></i> Toastr - إشعارات Toast</h2>

                        <div class="demo-buttons">
                            <button class="btn btn-success" onclick="showToastSuccess()">
                                <i class="fas fa-check"></i> نجاح
                            </button>
                            <button class="btn btn-danger" onclick="showToastError()">
                                <i class="fas fa-times"></i> خطأ
                            </button>
                            <button class="btn btn-warning" onclick="showToastWarning()">
                                <i class="fas fa-exclamation"></i> تحذير
                            </button>
                            <button class="btn btn-info" onclick="showToastInfo()">
                                <i class="fas fa-info"></i> معلومة
                            </button>
                        </div>
                    </div>

                    <!-- Chart.js -->
                    <div class="demo-section">
                        <h2><i class="fas fa-chart-bar"></i> Chart.js - الرسوم البيانية</h2>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <h3>رسم بياني خطي</h3>
                                <div class="chart-demo-container">
                                    <canvas id="lineChart"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <h3>رسم بياني شريطي</h3>
                                <div class="chart-demo-container">
                                    <canvas id="barChart"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <h3>رسم بياني دائري</h3>
                                <div class="chart-demo-container">
                                    <canvas id="pieChart"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <h3>رسم بياني دائري مفرغ</h3>
                                <div class="chart-demo-container">
                                    <canvas id="doughnutChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Font Awesome Icons -->
                    <div class="demo-section">
                        <h2><i class="fab fa-font-awesome"></i> Font Awesome - الأيقونات</h2>

                        <h3>أيقونات شائعة</h3>
                        <div class="d-flex flex-wrap">
                            <div class="icon-demo" title="home"><i class="fas fa-home"></i></div>
                            <div class="icon-demo" title="user"><i class="fas fa-user"></i></div>
                            <div class="icon-demo" title="cog"><i class="fas fa-cog"></i></div>
                            <div class="icon-demo" title="bell"><i class="fas fa-bell"></i></div>
                            <div class="icon-demo" title="envelope"><i class="fas fa-envelope"></i></div>
                            <div class="icon-demo" title="search"><i class="fas fa-search"></i></div>
                            <div class="icon-demo" title="heart"><i class="fas fa-heart"></i></div>
                            <div class="icon-demo" title="star"><i class="fas fa-star"></i></div>
                            <div class="icon-demo" title="shopping-cart"><i class="fas fa-shopping-cart"></i></div>
                            <div class="icon-demo" title="download"><i class="fas fa-download"></i></div>
                            <div class="icon-demo" title="upload"><i class="fas fa-upload"></i></div>
                            <div class="icon-demo" title="trash"><i class="fas fa-trash"></i></div>
                            <div class="icon-demo" title="edit"><i class="fas fa-edit"></i></div>
                            <div class="icon-demo" title="save"><i class="fas fa-save"></i></div>
                            <div class="icon-demo" title="print"><i class="fas fa-print"></i></div>
                            <div class="icon-demo" title="file"><i class="fas fa-file"></i></div>
                        </div>
                    </div>

                    <!-- Bootstrap Cards -->
                    <div class="demo-section">
                        <h2><i class="fab fa-bootstrap"></i> Bootstrap - البطاقات (Cards)</h2>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <i class="fas fa-info-circle"></i> بطاقة أساسية
                                    </div>
                                    <div class="card-body">
                                        <h5 class="card-title">عنوان البطاقة</h5>
                                        <p class="card-text">هذا نص تجريبي للبطاقة. يمكنك وضع أي محتوى هنا.</p>
                                        <a href="#" class="btn btn-primary">اقرأ المزيد</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <i class="fas fa-check-circle"></i> بطاقة نجاح
                                    </div>
                                    <div class="card-body">
                                        <h5 class="card-title">عملية ناجحة</h5>
                                        <p class="card-text">تمت العملية بنجاح وتم حفظ جميع البيانات.</p>
                                        <a href="#" class="btn btn-success">تأكيد</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-header bg-danger text-white">
                                        <i class="fas fa-exclamation-circle"></i> بطاقة تحذير
                                    </div>
                                    <div class="card-body">
                                        <h5 class="card-title">تحذير مهم</h5>
                                        <p class="card-text">يرجى الانتباه لهذا التحذير المهم.</p>
                                        <a href="#" class="btn btn-danger">عرض التفاصيل</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bootstrap Forms -->
                    <div class="demo-section">
                        <h2><i class="fab fa-bootstrap"></i> Bootstrap - النماذج (Forms)</h2>

                        <form>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="inputName" class="form-label">الاسم</label>
                                    <input type="text" class="form-control" id="inputName" placeholder="أدخل اسمك">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="inputEmail" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="inputEmail" placeholder="<EMAIL>">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="inputPassword" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="inputPassword" placeholder="كلمة المرور">
                            </div>

                            <div class="mb-3">
                                <label for="selectOption" class="form-label">اختر خياراً</label>
                                <select class="form-select" id="selectOption">
                                    <option selected>اختر...</option>
                                    <option value="1">الخيار 1</option>
                                    <option value="2">الخيار 2</option>
                                    <option value="3">الخيار 3</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="textareaMessage" class="form-label">رسالة</label>
                                <textarea class="form-control" id="textareaMessage" rows="3" placeholder="اكتب رسالتك هنا..."></textarea>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="checkAgree">
                                <label class="form-check-label" for="checkAgree">
                                    أوافق على الشروط والأحكام
                                </label>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="radioOptions" id="radio1" checked>
                                    <label class="form-check-label" for="radio1">
                                        الخيار الأول
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="radioOptions" id="radio2">
                                    <label class="form-check-label" for="radio2">
                                        الخيار الثاني
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> إرسال
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-redo"></i> إعادة تعيين
                            </button>
                        </form>
                    </div>

                    <!-- Bootstrap Badges & Progress -->
                    <div class="demo-section">
                        <h2><i class="fab fa-bootstrap"></i> Bootstrap - الشارات وأشرطة التقدم</h2>

                        <h3>الشارات (Badges)</h3>
                        <div class="mb-4">
                            <span class="badge bg-primary">أساسي</span>
                            <span class="badge bg-secondary">ثانوي</span>
                            <span class="badge bg-success">نجاح</span>
                            <span class="badge bg-danger">خطر</span>
                            <span class="badge bg-warning text-dark">تحذير</span>
                            <span class="badge bg-info text-dark">معلومة</span>
                            <span class="badge bg-light text-dark">فاتح</span>
                            <span class="badge bg-dark">داكن</span>
                        </div>

                        <h3>أشرطة التقدم (Progress Bars)</h3>
                        <div class="mb-2">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 25%">25%</div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="progress">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 50%">50%</div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="progress">
                                <div class="progress-bar bg-warning" role="progressbar" style="width: 75%">75%</div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="progress">
                                <div class="progress-bar bg-danger" role="progressbar" style="width: 100%">100%</div>
                            </div>
                        </div>
                    </div>

                    <!-- Bootstrap Tables -->
                    <div class="demo-section">
                        <h2><i class="fab fa-bootstrap"></i> Bootstrap - الجداول (Tables)</h2>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>أحمد محمد</td>
                                        <td><EMAIL></td>
                                        <td><span class="badge bg-success">نشط</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>فاطمة علي</td>
                                        <td><EMAIL></td>
                                        <td><span class="badge bg-warning">معلق</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>محمد حسن</td>
                                        <td><EMAIL></td>
                                        <td><span class="badge bg-danger">غير نشط</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary"><i class="fas fa-edit"></i></button>
                                            <button class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Bootstrap Accordion -->
                    <div class="demo-section">
                        <h2><i class="fab fa-bootstrap"></i> Bootstrap - الأكورديون (Accordion)</h2>

                        <div class="accordion" id="accordionExample">
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                        <i class="fas fa-question-circle ms-2"></i> ما هو نظام ERP؟
                                    </button>
                                </h2>
                                <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
                                    <div class="accordion-body">
                                        نظام تخطيط موارد المؤسسة (ERP) هو نوع من البرامج التي تستخدمها المؤسسات لإدارة أنشطة الأعمال اليومية.
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                                        <i class="fas fa-cog ms-2"></i> كيف يعمل النظام؟
                                    </button>
                                </h2>
                                <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                                    <div class="accordion-body">
                                        يعمل النظام من خلال دمج جميع العمليات التجارية في قاعدة بيانات واحدة.
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                                        <i class="fas fa-star ms-2"></i> ما هي المميزات؟
                                    </button>
                                </h2>
                                <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                                    <div class="accordion-body">
                                        يوفر النظام العديد من المميزات مثل إدارة المخزون، المبيعات، المشتريات، والموارد البشرية.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Modals -->

    <!-- 1. Basic Modal -->
    <div class="modal fade" id="basicModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-window-maximize"></i> نافذة أساسية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هذه نافذة منبثقة أساسية من Bootstrap.</p>
                    <p>يمكنك وضع أي محتوى هنا!</p>
                    <ul>
                        <li>سهلة الاستخدام</li>
                        <li>متجاوبة مع جميع الأجهزة</li>
                        <li>قابلة للتخصيص</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 2. Large Modal -->
    <div class="modal fade" id="largeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-expand"></i> نافذة كبيرة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>نافذة كبيرة الحجم (modal-lg)</h6>
                    <p>هذه نافذة كبيرة مناسبة لعرض محتوى أكثر.</p>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>العمود الأول</h6>
                            <p>محتوى العمود الأول</p>
                        </div>
                        <div class="col-md-6">
                            <h6>العمود الثاني</h6>
                            <p>محتوى العمود الثاني</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 3. Small Modal -->
    <div class="modal fade" id="smallModal" tabindex="-1">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-compress"></i> نافذة صغيرة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>نافذة صغيرة (modal-sm)</p>
                    <p>مناسبة للرسائل القصيرة</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary btn-sm" data-bs-dismiss="modal">حسناً</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 4. Extra Large Modal -->
    <div class="modal fade" id="xlModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-arrows-alt"></i> نافذة كبيرة جداً</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>نافذة كبيرة جداً (modal-xl)</h6>
                    <p>مناسبة لعرض جداول أو محتوى واسع</p>
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>أحمد محمد</td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-success">نشط</span></td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>فاطمة علي</td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-success">نشط</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 5. Fullscreen Modal -->
    <div class="modal fade" id="fullscreenModal" tabindex="-1">
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-desktop"></i> نافذة ملء الشاشة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h3>نافذة ملء الشاشة (modal-fullscreen)</h3>
                    <p>تملأ الشاشة بالكامل - مناسبة للتطبيقات والعروض التقديمية</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> يمكنك استخدام هذه النافذة لعرض محتوى كبير جداً
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 6. Scrollable Modal -->
    <div class="modal fade" id="scrollModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-scroll"></i> نافذة قابلة للتمرير</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>محتوى طويل جداً...</p>
                    <p>عندما يكون المحتوى طويلاً، يمكن تمرير النافذة بالكامل.</p>
                    <p>سطر 1</p><p>سطر 2</p><p>سطر 3</p><p>سطر 4</p><p>سطر 5</p>
                    <p>سطر 6</p><p>سطر 7</p><p>سطر 8</p><p>سطر 9</p><p>سطر 10</p>
                    <p>سطر 11</p><p>سطر 12</p><p>سطر 13</p><p>سطر 14</p><p>سطر 15</p>
                    <p>سطر 16</p><p>سطر 17</p><p>سطر 18</p><p>سطر 19</p><p>سطر 20</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 7. Scrollable Body Modal -->
    <div class="modal fade" id="scrollableBodyModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-arrows-alt-v"></i> تمرير المحتوى فقط</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>في هذه النافذة، المحتوى فقط قابل للتمرير (modal-dialog-scrollable)</p>
                    <p>الرأس والتذييل يبقيان ثابتين</p>
                    <p>سطر 1</p><p>سطر 2</p><p>سطر 3</p><p>سطر 4</p><p>سطر 5</p>
                    <p>سطر 6</p><p>سطر 7</p><p>سطر 8</p><p>سطر 9</p><p>سطر 10</p>
                    <p>سطر 11</p><p>سطر 12</p><p>سطر 13</p><p>سطر 14</p><p>سطر 15</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 8. Centered Modal -->
    <div class="modal fade" id="centeredModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-align-center"></i> نافذة متمركزة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هذه النافذة متمركزة عمودياً في الشاشة (modal-dialog-centered)</p>
                    <p>مناسبة للرسائل المهمة</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary">موافق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 9. Centered Scrollable Modal -->
    <div class="modal fade" id="centeredScrollModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-align-center"></i> متمركزة + قابلة للتمرير</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>نافذة متمركزة وقابلة للتمرير</p>
                    <p>سطر 1</p><p>سطر 2</p><p>سطر 3</p><p>سطر 4</p><p>سطر 5</p>
                    <p>سطر 6</p><p>سطر 7</p><p>سطر 8</p><p>سطر 9</p><p>سطر 10</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 10. Fade Modal (Default Animation) -->
    <div class="modal fade" id="fadeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-magic"></i> Fade Animation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هذه هي الحركة الافتراضية (fade)</p>
                    <p>تظهر النافذة بتلاشي تدريجي</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">حسناً</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 11. Slide Modal -->
    <div class="modal" id="slideModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-arrow-down"></i> Slide Animation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>النافذة تنزلق من الأعلى</p>
                    <p>حركة سلسة واحترافية</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" data-bs-dismiss="modal">رائع!</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 12. Zoom Modal -->
    <div class="modal" id="zoomModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-search-plus"></i> Zoom Animation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>النافذة تكبر من المركز</p>
                    <p>تأثير Zoom مميز</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-info" data-bs-dismiss="modal">ممتاز!</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 13. Flip Modal -->
    <div class="modal" id="flipModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-sync"></i> Flip Animation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>النافذة تدور بحركة Flip</p>
                    <p>تأثير ثلاثي الأبعاد</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-warning" data-bs-dismiss="modal">جميل!</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 14. Bounce Modal -->
    <div class="modal" id="bounceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-basketball-ball"></i> Bounce Animation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>النافذة تظهر بحركة ارتداد</p>
                    <p>تأثير مرح وجذاب</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">رائع!</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 15. No Backdrop Modal -->
    <div class="modal fade" id="noBackdropModal" tabindex="-1" data-bs-backdrop="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-ban"></i> بدون خلفية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هذه النافذة بدون خلفية داكنة (backdrop="false")</p>
                    <p>يمكنك التفاعل مع الصفحة خلف النافذة</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 16. Static Backdrop Modal -->
    <div class="modal fade" id="staticBackdropModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-lock"></i> خلفية ثابتة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هذه النافذة لا تغلق عند الضغط على الخلفية (backdrop="static")</p>
                    <p>يجب الضغط على زر الإغلاق</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> مناسبة للنماذج المهمة
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 17. Form Modal -->
    <div class="modal fade" id="formModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit"></i> نافذة مع نموذج</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="userName" class="form-label">الاسم</label>
                            <input type="text" class="form-control" id="userName" placeholder="أدخل اسمك">
                        </div>
                        <div class="mb-3">
                            <label for="userEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="userEmail" placeholder="<EMAIL>">
                        </div>
                        <div class="mb-3">
                            <label for="userMessage" class="form-label">الرسالة</label>
                            <textarea class="form-control" id="userMessage" rows="3" placeholder="اكتب رسالتك هنا"></textarea>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="agreeCheck">
                            <label class="form-check-label" for="agreeCheck">
                                أوافق على الشروط والأحكام
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary">إرسال</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 18. Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="loginEmail" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" class="form-control" id="loginEmail" placeholder="<EMAIL>">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="loginPassword" placeholder="••••••••">
                            </div>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                تذكرني
                            </label>
                        </div>
                    </form>
                    <div class="text-center">
                        <a href="#" class="text-decoration-none">نسيت كلمة المرور؟</a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success">تسجيل الدخول</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 19. First Modal (for nested modals) -->
    <div class="modal fade" id="firstModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-layer-group"></i> النافذة الأولى</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هذه هي النافذة الأولى</p>
                    <p>يمكنك فتح نافذة ثانية من هنا</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#secondModal">
                        فتح النافذة الثانية
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 20. Second Modal (nested) -->
    <div class="modal fade" id="secondModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title"><i class="fas fa-check-circle"></i> النافذة الثانية</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هذه نافذة متداخلة (Nested Modal)</p>
                    <p>تم فتحها من النافذة الأولى</p>
                    <div class="alert alert-success">
                        <i class="fas fa-info-circle"></i> يمكنك فتح عدة نوافذ متداخلة
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 21. Primary Colored Modal -->
    <div class="modal fade" id="primaryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title"><i class="fas fa-palette"></i> نافذة زرقاء</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>نافذة بلون أزرق (Primary)</p>
                    <p>مناسبة للمعلومات العامة</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">حسناً</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 22. Success Colored Modal -->
    <div class="modal fade" id="successModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title"><i class="fas fa-check-circle"></i> نافذة خضراء</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>نافذة بلون أخضر (Success)</p>
                    <p>مناسبة لرسائل النجاح</p>
                    <div class="alert alert-success">
                        <i class="fas fa-check"></i> تمت العملية بنجاح!
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" data-bs-dismiss="modal">رائع!</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 23. Danger Colored Modal -->
    <div class="modal fade" id="dangerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title"><i class="fas fa-exclamation-circle"></i> نافذة حمراء</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>نافذة بلون أحمر (Danger)</p>
                    <p>مناسبة للتحذيرات والأخطاء</p>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> تحذير: هذا إجراء خطير!
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger">تأكيد الحذف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 24. Warning Colored Modal -->
    <div class="modal fade" id="warningModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title"><i class="fas fa-exclamation-triangle"></i> نافذة صفراء</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>نافذة بلون أصفر (Warning)</p>
                    <p>مناسبة للتنبيهات</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle"></i> انتبه: يرجى مراجعة البيانات
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-warning">فهمت</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 25. Info Colored Modal -->
    <div class="modal fade" id="infoModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title"><i class="fas fa-info-circle"></i> نافذة سماوية</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>نافذة بلون سماوي (Info)</p>
                    <p>مناسبة للمعلومات الإضافية</p>
                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb"></i> نصيحة: يمكنك تخصيص الألوان حسب رغبتك
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-info" data-bs-dismiss="modal">شكراً</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Styles for Modal Animations -->
    <style>
        /* Slide Animation */
        .modal.slide .modal-dialog {
            transform: translateY(-100%);
            transition: transform 0.3s ease-out;
        }

        .modal.slide.show .modal-dialog {
            transform: translateY(0);
        }

        /* Zoom Animation */
        .modal.zoom .modal-dialog {
            transform: scale(0.1);
            transition: transform 0.3s ease-out;
        }

        .modal.zoom.show .modal-dialog {
            transform: scale(1);
        }

        /* Flip Animation */
        .modal.flip .modal-dialog {
            transform: perspective(1000px) rotateX(-90deg);
            transition: transform 0.4s ease-out;
        }

        .modal.flip.show .modal-dialog {
            transform: perspective(1000px) rotateX(0);
        }

        /* Bounce Animation */
        @keyframes bounceIn {
            0% {
                transform: scale(0.3);
                opacity: 0;
            }
            50% {
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .modal.bounce.show .modal-dialog {
            animation: bounceIn 0.5s ease-out;
        }
    </style>

    <!-- Scripts -->
    <script src="../assets/libs/jquery/jquery.min.js"></script>
    <script src="../assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/libs/sweetalert2/sweetalert2.min.js"></script>
    <script src="../assets/libs/toastr/toastr.min.js"></script>
    <script src="../assets/libs/chartjs/chart.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/components-demo.js"></script>
</body>
</html>

