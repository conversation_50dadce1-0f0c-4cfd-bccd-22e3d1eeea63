/* 
 * ERP Dashboard - Mobile Enhancements
 * تحسينات إضافية للهواتف المحمولة والشاشات الصغيرة
 */

/* ==================== Touch-Friendly Improvements ==================== */

/* Increase touch targets for better usability */
@media (max-width: 767.98px) {
    /* Buttons */
    .btn {
        min-height: 44px;
        min-width: 44px;
        touch-action: manipulation;
    }

    .btn-sm {
        min-height: 36px;
        min-width: 36px;
    }

    .btn-lg {
        min-height: 52px;
        min-width: 52px;
    }

    /* Form controls */
    .form-control,
    .form-select {
        min-height: 44px;
        touch-action: manipulation;
    }

    /* Navigation links */
    .nav-link {
        min-height: 44px;
        touch-action: manipulation;
    }

    /* Navbar actions - ensure consistency */
    .navbar-action,
    .navbar-menu-toggle {
        min-height: 44px;
        min-width: 44px;
        touch-action: manipulation;
    }

    /* Sidebar brand consistency */
    .sidebar-brand {
        min-height: var(--navbar-height);
        display: flex;
        align-items: center;
    }

    /* Ensure navbar and sidebar alignment */
    .navbar,
    .sidebar-brand {
        height: var(--navbar-height);
    }
}

/* ==================== Improved Scrolling ==================== */

/* Smooth scrolling for mobile */
.table-responsive,
.sidebar,
.navbar-dropdown-menu {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
@media (max-width: 767.98px) {
    .table-responsive::-webkit-scrollbar {
        height: 4px;
    }
    
    .table-responsive::-webkit-scrollbar-track {
        background: var(--bg-secondary);
        border-radius: 2px;
    }
    
    .table-responsive::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 2px;
    }
    
    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: var(--primary-dark);
    }
}

/* ==================== Mobile-Specific Components ==================== */

/* Mobile card improvements */
@media (max-width: 767.98px) {
    .card {
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        margin-bottom: var(--spacing-md);
    }
    
    .card-header {
        padding: var(--spacing-md);
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    }
    
    .card-body {
        padding: var(--spacing-md);
    }
    
    .card-footer {
        padding: var(--spacing-md);
        border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    }
}

/* Mobile modal improvements */
@media (max-width: 767.98px) {
    .modal-dialog {
        margin: var(--spacing-sm);
        max-width: calc(100vw - 2 * var(--spacing-sm));
    }
    
    .modal-content {
        border-radius: var(--radius-lg);
    }
    
    .modal-header {
        padding: var(--spacing-md);
    }
    
    .modal-body {
        padding: var(--spacing-md);
        max-height: calc(100vh - 200px);
        overflow-y: auto;
    }
    
    .modal-footer {
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .modal-footer .btn {
        width: 100%;
    }
}

/* ==================== Mobile Typography ==================== */

@media (max-width: 767.98px) {
    /* Improve readability on small screens */
    body {
        line-height: 1.5;
    }
    
    /* Adjust heading sizes for mobile */
    .page-title {
        font-size: var(--font-size-2xl);
        line-height: 1.2;
        margin-bottom: var(--spacing-sm);
    }
    
    .page-subtitle {
        font-size: var(--font-size-sm);
        line-height: 1.4;
    }
    
    /* Card titles */
    .card-title {
        font-size: var(--font-size-base);
        line-height: 1.3;
    }
    
    /* Table text */
    .table {
        font-size: var(--font-size-sm);
        line-height: 1.3;
    }
}

/* ==================== Mobile Spacing ==================== */

@media (max-width: 767.98px) {
    /* Reduce margins and padding for mobile */
    .stats-grid {
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }
    
    .dashboard-grid {
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }
    
    /* Content wrapper spacing */
    .content-wrapper {
        padding: var(--spacing-sm);
    }
}

@media (max-width: 575.98px) {
    /* Further reduce spacing for very small screens */
    .content-wrapper {
        padding: var(--spacing-xs);
    }
    
    .stats-grid,
    .dashboard-grid {
        gap: var(--spacing-sm);
    }
}

/* ==================== Mobile Animations ==================== */

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Smooth transitions for mobile interactions */
@media (max-width: 767.98px) {
    .btn,
    .nav-link,
    .navbar-action,
    .card {
        transition: all 0.2s ease-in-out;
    }
    
    /* Hover effects for touch devices */
    .btn:active,
    .nav-link:active,
    .navbar-action:active {
        transform: scale(0.98);
    }
}

/* ==================== Mobile Accessibility ==================== */

/* Focus indicators for keyboard navigation */
@media (max-width: 767.98px) {
    .btn:focus,
    .nav-link:focus,
    .navbar-action:focus,
    .form-control:focus,
    .form-select:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card,
    .table,
    .btn {
        border: 2px solid var(--text-primary);
    }
}

/* ==================== Mobile Print Styles ==================== */

@media print {
    /* Hide navigation elements when printing */
    .sidebar,
    .navbar,
    .navbar-action,
    .btn {
        display: none !important;
    }
    
    /* Adjust layout for printing */
    .main-content {
        margin: 0 !important;
    }
    
    .content-wrapper {
        padding: 0 !important;
    }
    
    /* Ensure tables fit on printed page */
    .table {
        font-size: 10px;
        min-width: auto;
    }
    
    .table th,
    .table td {
        padding: 0.25rem;
        white-space: normal;
    }
}
