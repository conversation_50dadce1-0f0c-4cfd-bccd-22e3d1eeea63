/* 
 * ERP Dashboard - RTL/LTR Support
 * دعم الاتجاهين العربي والإنجليزي
 */

/* ==================== Language Toggle Button ==================== */

.language-dropdown-menu {
    min-width: 280px;
}

.language-option {
    position: relative;
}

.language-check {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    opacity: 0;
    transition: var(--transition-fast);
}

.language-option.active .language-check {
    opacity: 1;
}

.language-text {
    margin-right: 0.5rem;
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
}

/* ==================== LTR Overrides ==================== */

[dir="ltr"] {
    text-align: left;
}

/* Sidebar LTR */
[dir="ltr"] .sidebar {
    right: auto;
    left: 0;
}

[dir="ltr"] .sidebar-toggle {
    left: auto;
    right: -20px;
}

[dir="ltr"] .sidebar-toggle i {
    transform: rotate(180deg);
}

[dir="ltr"] .sidebar.collapsed .sidebar-toggle i {
    transform: rotate(0deg);
}

/* Main Content LTR */
[dir="ltr"] .main-content {
    margin-right: 0;
    margin-left: var(--sidebar-width);
}

[dir="ltr"] .main-content.sidebar-collapsed {
    margin-left: var(--sidebar-collapsed-width);
}

/* Navbar LTR */
[dir="ltr"] .navbar {
    right: auto;
    left: var(--sidebar-width);
}

[dir="ltr"] .navbar.sidebar-collapsed {
    left: var(--sidebar-collapsed-width);
}

[dir="ltr"] .navbar-left {
    margin-left: 0;
    margin-right: auto;
}

[dir="ltr"] .navbar-right {
    margin-right: 0;
    margin-left: auto;
}

[dir="ltr"] .navbar-search-input {
    padding: 0.625rem 3rem 0.625rem 1rem;
}

[dir="ltr"] .navbar-search-icon {
    right: auto;
    left: 1rem;
}

/* Dropdown LTR */
[dir="ltr"] .navbar-dropdown-menu {
    right: auto;
    left: 0;
}

[dir="ltr"] .navbar-dropdown-menu::before {
    right: auto;
    left: 1rem;
}

/* Navigation LTR */
[dir="ltr"] .nav-link {
    text-align: left;
}

[dir="ltr"] .nav-link-icon {
    margin-right: 0;
    margin-left: var(--spacing-md);
    order: 2;
}

[dir="ltr"] .nav-link-text {
    order: 1;
}

[dir="ltr"] .nav-link-badge {
    margin-right: auto;
    margin-left: 0;
    order: 3;
}

/* Popup Menus LTR */
[dir="ltr"] .nav-popup {
    right: auto;
    left: calc(100% + 10px);
}

[dir="ltr"] .nav-popup::before {
    right: auto;
    left: -8px;
    border-right: 8px solid var(--sidebar-bg);
    border-left: none;
}

/* Cards LTR */
[dir="ltr"] .stat-card-icon {
    margin-right: 0;
    margin-left: var(--spacing-lg);
    order: 2;
}

[dir="ltr"] .stat-card-content {
    order: 1;
}

/* Tables LTR */
[dir="ltr"] .table {
    text-align: left;
}

[dir="ltr"] .table th,
[dir="ltr"] .table td {
    text-align: left;
}

/* Forms LTR */
[dir="ltr"] .form-label {
    text-align: left;
}

[dir="ltr"] .form-control {
    text-align: left;
}

/* Buttons LTR */
[dir="ltr"] .btn i {
    margin-right: 0;
    margin-left: 0.5rem;
    order: 2;
}

[dir="ltr"] .btn span {
    order: 1;
}

/* Breadcrumb LTR */
[dir="ltr"] .breadcrumb-item + .breadcrumb-item::before {
    content: "/";
    margin: 0 0.5rem;
}

/* Modal LTR */
[dir="ltr"] .modal-header .btn-close {
    margin: -0.5rem -0.5rem -0.5rem auto;
}

/* ==================== Mobile Responsive LTR ==================== */

@media (max-width: 991.98px) {
    [dir="ltr"] .main-content {
        margin-left: 0 !important;
    }

    [dir="ltr"] .main-content.sidebar-collapsed {
        margin-left: 0 !important;
    }

    [dir="ltr"] .navbar {
        left: 0 !important;
    }

    [dir="ltr"] .navbar.sidebar-collapsed {
        left: 0 !important;
    }

    /* Fix sidebar positioning for LTR on mobile */
    [dir="ltr"] .sidebar {
        left: 0;
        right: auto;
        transform: translateX(-100%);
    }

    [dir="ltr"] .sidebar.show {
        transform: translateX(0);
    }
}

/* Mobile RTL fixes */
@media (max-width: 991.98px) {
    [dir="rtl"] .sidebar {
        right: 0;
        left: auto;
        transform: translateX(100%);
    }

    [dir="rtl"] .sidebar.show {
        transform: translateX(0);
    }
}

/* Additional mobile responsive fixes */
@media (max-width: 767.98px) {
    /* Fix form select arrow position for RTL */
    [dir="rtl"] .form-select {
        background-position: right 0.75rem center;
        padding-right: 2.5rem;
        padding-left: 1rem;
    }

    [dir="ltr"] .form-select {
        background-position: left 0.75rem center;
        padding-left: 2.5rem;
        padding-right: 1rem;
    }

    /* Fix navbar search positioning */
    [dir="rtl"] .navbar-search-icon {
        right: 1rem;
        left: auto;
    }

    [dir="ltr"] .navbar-search-icon {
        left: 1rem;
        right: auto;
    }

    /* Fix dropdown menu positioning on mobile */
    [dir="rtl"] .navbar-dropdown-menu {
        right: 0;
        left: auto;
    }

    [dir="ltr"] .navbar-dropdown-menu {
        left: 0;
        right: auto;
    }

    /* Fix user dropdown positioning */
    [dir="rtl"] .navbar-user-dropdown .navbar-dropdown-menu {
        right: 0;
        left: auto;
    }

    [dir="ltr"] .navbar-user-dropdown .navbar-dropdown-menu {
        right: 0;
        left: auto;
    }
}

/* ==================== Language-Specific Fonts ==================== */

[dir="ltr"] {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

[dir="rtl"] {
    font-family: var(--font-family);
}

/* ==================== Language Toggle Animation ==================== */

.language-switching {
    transition: all 0.3s ease-in-out;
}

.language-switching * {
    transition: all 0.3s ease-in-out;
}

/* ==================== Bootstrap RTL/LTR Fixes ==================== */

/* Fix Bootstrap components for LTR */
[dir="ltr"] .dropdown-menu {
    right: auto;
    left: 0;
}

[dir="ltr"] .dropdown-toggle::after {
    margin-left: 0.255em;
    margin-right: 0;
}

[dir="ltr"] .nav-pills .nav-link {
    text-align: left;
}

[dir="ltr"] .alert-dismissible .btn-close {
    right: 0;
    left: auto;
}

/* Fix for sidebar submenu arrows */
[dir="ltr"] .nav-link.has-submenu::after {
    right: auto;
    left: 1rem;
    transform: rotate(-90deg);
}

[dir="ltr"] .nav-link.has-submenu.expanded::after {
    transform: rotate(0deg);
}

/* Fix RTL submenu arrows */
[dir="rtl"] .nav-link.has-submenu::after {
    right: 1rem;
    left: auto;
    transform: rotate(0deg);
}

[dir="rtl"] .nav-link.has-submenu.open::after {
    transform: rotate(180deg);
}

/* Fix badge positioning */
[dir="ltr"] .nav-link-badge {
    margin-left: auto;
    margin-right: 0;
}

[dir="rtl"] .nav-link-badge {
    margin-right: auto;
    margin-left: 0;
}

/* Fix stat card layout */
[dir="ltr"] .stat-card-header {
    flex-direction: row;
}

[dir="rtl"] .stat-card-header {
    flex-direction: row-reverse;
}

/* Fix activity item layout */
[dir="ltr"] .activity-item {
    flex-direction: row;
}

[dir="rtl"] .activity-item {
    flex-direction: row-reverse;
}

[dir="ltr"] .activity-time {
    margin-left: auto;
}

[dir="rtl"] .activity-time {
    margin-right: auto;
}

/* Fix navbar action badge positioning */
[dir="ltr"] .navbar-action-badge {
    right: 4px;
    left: auto;
}

[dir="rtl"] .navbar-action-badge {
    left: 4px;
    right: auto;
}

/* Fix language check positioning */
[dir="ltr"] .language-check {
    right: 1rem;
    left: auto;
}

[dir="rtl"] .language-check {
    left: 1rem;
    right: auto;
}

/* ==================== Text Direction Utilities ==================== */

.text-ltr {
    direction: ltr;
    text-align: left;
}

.text-rtl {
    direction: rtl;
    text-align: right;
}

/* ==================== Language-Specific Content ==================== */

.lang-ar {
    display: block;
}

.lang-en {
    display: none;
}

[dir="ltr"] .lang-ar {
    display: none;
}

[dir="ltr"] .lang-en {
    display: block;
}

/* ==================== Print Styles ==================== */

@media print {
    [dir="ltr"] .sidebar {
        left: 0;
        right: auto;
    }
    
    [dir="ltr"] .main-content {
        margin-left: 0;
        margin-right: 0;
    }
}
