/**
 * ERP Dashboard - Main JavaScript
 * الملف الرئيسي للجافاسكريبت
 */

(function($) {
    'use strict';

    // ==================== Global Variables ====================
    const sidebar = $('#sidebar');
    const mainContent = $('#mainContent');
    const navbar = $('#navbar');
    const sidebarToggle = $('#sidebarToggle');
    const mobileMenuToggle = $('#mobileMenuToggle');
    const sidebarOverlay = $('#sidebarOverlay');
    const languageToggle = $('#languageToggle');
    const languageDropdown = $('#languageDropdown');

    // ==================== Sidebar Toggle ====================
    function toggleSidebar() {
        sidebar.toggleClass('collapsed');
        mainContent.toggleClass('sidebar-collapsed');
        navbar.toggleClass('sidebar-collapsed');
        
        // Save state to localStorage
        const isCollapsed = sidebar.hasClass('collapsed');
        localStorage.setItem('sidebarCollapsed', isCollapsed);
    }

    // Restore sidebar state from localStorage
    function restoreSidebarState() {
        const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        if (isCollapsed) {
            sidebar.addClass('collapsed');
            mainContent.addClass('sidebar-collapsed');
            navbar.addClass('sidebar-collapsed');
        }
    }

    // Desktop sidebar toggle
    sidebarToggle.on('click', function(e) {
        e.preventDefault();
        toggleSidebar();
    });

    // Mobile menu toggle
    mobileMenuToggle.on('click', function(e) {
        e.preventDefault();
        sidebar.toggleClass('show');
        sidebarOverlay.toggleClass('show');
        $('body').toggleClass('sidebar-open');
    });

    // Close sidebar on overlay click (mobile)
    sidebarOverlay.on('click', function() {
        sidebar.removeClass('show');
        sidebarOverlay.removeClass('show');
        $('body').removeClass('sidebar-open');
    });

    // ==================== Submenu Toggle ====================
    $('.nav-item.has-submenu > .nav-link').on('click', function(e) {
        e.preventDefault();
        const navItem = $(this).parent();
        const isOpen = navItem.hasClass('open');
        
        // Close all other submenus
        $('.nav-item.has-submenu').not(navItem).removeClass('open');
        
        // Toggle current submenu
        navItem.toggleClass('open');
    });

    // ==================== Dropdown Toggle ====================
    $('.navbar-dropdown-toggle, .navbar-action').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const dropdown = $(this).closest('.navbar-dropdown');
        const isOpen = dropdown.hasClass('show');
        
        // Close all dropdowns
        $('.navbar-dropdown').removeClass('show');
        
        // Toggle current dropdown
        if (!isOpen) {
            dropdown.addClass('show');
        }
    });

    // Close dropdowns when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.navbar-dropdown').length) {
            $('.navbar-dropdown').removeClass('show');
        }
    });

    // Prevent dropdown from closing when clicking inside
    $('.navbar-dropdown-menu').on('click', function(e) {
        e.stopPropagation();
    });

    // ==================== Toastr Configuration ====================
    if (typeof toastr !== 'undefined') {
        toastr.options = {
            closeButton: true,
            debug: false,
            newestOnTop: true,
            progressBar: true,
            positionClass: 'toast-top-left',
            preventDuplicates: false,
            onclick: null,
            showDuration: '300',
            hideDuration: '1000',
            timeOut: '5000',
            extendedTimeOut: '1000',
            showEasing: 'swing',
            hideEasing: 'linear',
            showMethod: 'fadeIn',
            hideMethod: 'fadeOut',
            rtl: true
        };
    }

    // ==================== SweetAlert2 Configuration ====================
    if (typeof Swal !== 'undefined') {
        // Set default options for SweetAlert2
        const swalWithBootstrap = Swal.mixin({
            customClass: {
                confirmButton: 'btn btn-primary me-2',
                cancelButton: 'btn btn-secondary'
            },
            buttonsStyling: false,
            reverseButtons: true
        });

        // Make it globally available
        window.swalWithBootstrap = swalWithBootstrap;
    }

    // ==================== Utility Functions ====================

    /**
     * Show success notification
     */
    window.showSuccess = function(message, title = 'نجح!') {
        if (typeof toastr !== 'undefined') {
            toastr.success(message, title);
        }
    };

    /**
     * Show error notification
     */
    window.showError = function(message, title = 'خطأ!') {
        if (typeof toastr !== 'undefined') {
            toastr.error(message, title);
        }
    };

    /**
     * Show warning notification
     */
    window.showWarning = function(message, title = 'تحذير!') {
        if (typeof toastr !== 'undefined') {
            toastr.warning(message, title);
        }
    };

    /**
     * Show info notification
     */
    window.showInfo = function(message, title = 'معلومة') {
        if (typeof toastr !== 'undefined') {
            toastr.info(message, title);
        }
    };

    /**
     * Confirm dialog
     */
    window.confirmDialog = function(options) {
        if (typeof Swal !== 'undefined') {
            return Swal.fire({
                title: options.title || 'هل أنت متأكد؟',
                text: options.text || 'لن تتمكن من التراجع عن هذا الإجراء!',
                icon: options.icon || 'warning',
                showCancelButton: true,
                confirmButtonText: options.confirmText || 'نعم، متأكد',
                cancelButtonText: options.cancelText || 'إلغاء',
                reverseButtons: true
            });
        }
        return Promise.resolve({ isConfirmed: confirm(options.text) });
    };

    /**
     * Format number with thousands separator
     */
    window.formatNumber = function(number) {
        return new Intl.NumberFormat('ar-SA').format(number);
    };

    /**
     * Format currency
     */
    window.formatCurrency = function(amount, currency = 'ر.س') {
        return formatNumber(amount) + ' ' + currency;
    };

    /**
     * Format date
     */
    window.formatDate = function(date, format = 'short') {
        const d = new Date(date);
        const options = format === 'short' 
            ? { year: 'numeric', month: '2-digit', day: '2-digit' }
            : { year: 'numeric', month: 'long', day: 'numeric' };
        return new Intl.DateTimeFormat('ar-SA', options).format(d);
    };

    /**
     * Debounce function
     */
    window.debounce = function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    };

    // ==================== Search Functionality ====================
    const searchInput = $('.navbar-search-input');
    if (searchInput.length) {
        searchInput.on('keyup', debounce(function() {
            const query = $(this).val().trim();
            if (query.length >= 3) {
                // Implement search functionality here
                console.log('Searching for:', query);
            }
        }, 300));
    }

    // ==================== Responsive Handling ====================
    function handleResize() {
        const width = $(window).width();
        
        // Close mobile sidebar on resize to desktop
        if (width >= 992) {
            sidebar.removeClass('show');
            sidebarOverlay.removeClass('show');
            $('body').removeClass('sidebar-open');
        }
    }

    $(window).on('resize', debounce(handleResize, 250));

    // ==================== Smooth Scroll ====================
    $('a[href^="#"]').on('click', function(e) {
        const target = $(this.getAttribute('href'));
        if (target.length) {
            e.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });

    // ==================== Initialize ====================
    function init() {
        // Restore sidebar state
        restoreSidebarState();
        
        // Show welcome notification
        setTimeout(function() {
            if (typeof toastr !== 'undefined') {
                showInfo('مرحباً بك في نظام إدارة موارد المؤسسة', 'مرحباً!');
            }
        }, 1000);
    }

    // ==================== Language Toggle ====================

    // Language translations
    const translations = {
        ar: {
            // Navbar
            searchPlaceholder: 'ابحث في النظام...',
            notifications: 'الإشعارات',
            messages: 'الرسائل',
            language: 'اللغة',
            chooseLanguage: 'اختر اللغة',
            arabic: 'العربية',
            english: 'English',
            arabicDesc: 'Arabic - من اليمين إلى اليسار',
            englishDesc: 'English - Left to Right',

            // Sidebar
            mainMenu: 'القائمة الرئيسية',
            dashboard: 'لوحة التحكم',
            reports: 'التقارير',
            notifications_menu: 'الإشعارات',
            management: 'الإدارة',
            employees: 'إدارة الموظفين',
            inventory: 'إدارة المخزون',
            invoices: 'الفواتير',
            sales: 'المبيعات',
            finance: 'المالية',
            pages: 'الصفحات',
            pos: 'نقطة البيع POS',
            products: 'المنتجات',
            invoices_page: 'الفواتير',
            demoPages: 'صفحات العرض',
            components: 'عرض المكونات',
            jquery: 'عرض jQuery',
            samplePage: 'صفحة نموذجية',
            settings: 'الإعدادات',
            systemSettings: 'إعدادات النظام',
            permissions: 'الصلاحيات',

            // Dashboard
            totalSales: 'إجمالي المبيعات',
            totalOrders: 'إجمالي الطلبات',
            totalCustomers: 'إجمالي العملاء',
            totalProducts: 'إجمالي المنتجات',
            salesChart: 'مخطط المبيعات',
            revenueChart: 'مخطط الإيرادات',

            // User
            userName: 'أحمد محمد',
            userRole: 'مدير النظام'
        },
        en: {
            // Navbar
            searchPlaceholder: 'Search in system...',
            notifications: 'Notifications',
            messages: 'Messages',
            language: 'Language',
            chooseLanguage: 'Choose Language',
            arabic: 'العربية',
            english: 'English',
            arabicDesc: 'Arabic - من اليمين إلى اليسار',
            englishDesc: 'English - Left to Right',

            // Sidebar
            mainMenu: 'Main Menu',
            dashboard: 'Dashboard',
            reports: 'Reports',
            notifications_menu: 'Notifications',
            management: 'Management',
            employees: 'Employee Management',
            inventory: 'Inventory Management',
            invoices: 'Invoices',
            sales: 'Sales',
            finance: 'Finance',
            pages: 'Pages',
            pos: 'POS System',
            products: 'Products',
            invoices_page: 'Invoices',
            demoPages: 'Demo Pages',
            components: 'Components Demo',
            jquery: 'jQuery Demo',
            samplePage: 'Sample Page',
            settings: 'Settings',
            systemSettings: 'System Settings',
            permissions: 'Permissions',

            // Dashboard
            totalSales: 'Total Sales',
            totalOrders: 'Total Orders',
            totalCustomers: 'Total Customers',
            totalProducts: 'Total Products',
            salesChart: 'Sales Chart',
            revenueChart: 'Revenue Chart',

            // User
            userName: 'Ahmed Mohamed',
            userRole: 'System Admin'
        }
    };

    // Get current language from localStorage or default to Arabic
    function getCurrentLanguage() {
        return localStorage.getItem('language') || 'ar';
    }

    // Get current direction from localStorage or default to RTL
    function getCurrentDirection() {
        return localStorage.getItem('direction') || 'rtl';
    }

    // Set language and direction
    function setLanguage(lang, dir) {
        // Add transition class
        $('body').addClass('language-switching');

        // Update HTML attributes
        $('html').attr('lang', lang).attr('dir', dir);

        // Update Bootstrap CSS
        if (dir === 'rtl') {
            $('link[href*="bootstrap"]').attr('href', 'assets/libs/bootstrap/css/bootstrap.rtl.min.css');
        } else {
            $('link[href*="bootstrap"]').attr('href', 'assets/libs/bootstrap/css/bootstrap.min.css');
        }

        // Update text content
        updateTextContent(lang);

        // Update language toggle button
        updateLanguageToggle(lang);

        // Update active language option
        updateActiveLanguageOption(lang);

        // Save to localStorage
        localStorage.setItem('language', lang);
        localStorage.setItem('direction', dir);

        // Remove transition class after animation
        setTimeout(() => {
            $('body').removeClass('language-switching');
        }, 300);
    }

    // Update text content based on language
    function updateTextContent(lang) {
        const t = translations[lang];

        // Navbar
        $('.navbar-search-input').attr('placeholder', t.searchPlaceholder);
        $('#notificationsDropdown .navbar-dropdown-title').text(t.notifications);
        $('#languageDropdown .navbar-dropdown-title').text(t.chooseLanguage);
        $('.language-text').text(lang === 'ar' ? t.arabic : t.english);

        // Sidebar sections
        $('.nav-section-title').each(function(index) {
            const titles = [t.mainMenu, t.management, t.pages, t.demoPages, t.settings];
            if (titles[index]) {
                $(this).text(titles[index]);
            }
        });

        // Navigation items
        $('[data-translate]').each(function() {
            const key = $(this).data('translate');
            if (t[key]) {
                $(this).text(t[key]);
            }
        });

        // User info
        $('.navbar-user-name, .sidebar-user-name').text(t.userName);
        $('.navbar-user-role, .sidebar-user-role').text(t.userRole);
    }

    // Update language toggle button
    function updateLanguageToggle(lang) {
        const t = translations[lang];
        languageToggle.attr('title',
            lang === 'ar' ? 'تغيير اللغة / Change Language' : 'Change Language / تغيير اللغة'
        );
    }

    // Update active language option
    function updateActiveLanguageOption(lang) {
        $('.language-option').removeClass('active');
        $(`.language-option[data-lang="${lang}"]`).addClass('active');
    }

    // Initialize language system
    function initLanguage() {
        const currentLang = getCurrentLanguage();
        const currentDir = getCurrentDirection();

        // Set initial language without animation
        $('html').attr('lang', currentLang).attr('dir', currentDir);

        // Load appropriate Bootstrap CSS
        if (currentDir === 'rtl') {
            $('link[href*="bootstrap"]').attr('href', 'assets/libs/bootstrap/css/bootstrap.rtl.min.css');
        } else {
            $('link[href*="bootstrap"]').attr('href', 'assets/libs/bootstrap/css/bootstrap.min.css');
        }

        // Update content
        updateTextContent(currentLang);
        updateLanguageToggle(currentLang);
        updateActiveLanguageOption(currentLang);
    }

    // Language toggle click handler
    languageToggle.on('click', function(e) {
        e.preventDefault();
        languageDropdown.find('.navbar-dropdown-menu').toggleClass('show');
    });

    // Language option click handler
    $(document).on('click', '.language-option', function(e) {
        e.preventDefault();

        const lang = $(this).data('lang');
        const dir = $(this).data('dir');

        setLanguage(lang, dir);

        // Hide dropdown
        languageDropdown.find('.navbar-dropdown-menu').removeClass('show');
    });

    // ==================== Document Ready ====================
    $(document).ready(function() {
        initLanguage();
        init();
    });

})(jQuery);

