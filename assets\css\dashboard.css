/* 
 * ERP Dashboard - Dashboard Page Styles
 * أنماط صفحة لوحة التحكم
 */

/* ==================== Page Header ==================== */

.page-header {
    margin-bottom: var(--spacing-xl);
}

.page-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.page-subtitle {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin: 0;
}

/* ==================== Statistics Cards ==================== */

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--card-bg);
    border-radius: var(--card-border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-2px);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--stat-color), transparent);
}

.stat-card.stat-primary { --stat-color: var(--primary-color); }
.stat-card.stat-success { --stat-color: var(--success-color); }
.stat-card.stat-warning { --stat-color: var(--warning-color); }
.stat-card.stat-danger { --stat-color: var(--danger-color); }
.stat-card.stat-info { --stat-color: var(--info-color); }

.stat-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.stat-card-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
    color: var(--text-secondary);
    margin: 0;
}

.stat-card-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--stat-color), var(--stat-color-light, var(--stat-color)));
    color: white;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xl);
}

.stat-card-body {
    margin-bottom: var(--spacing-sm);
}

.stat-card-value {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.stat-card-label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.stat-card-footer {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xs);
}

.stat-card-change {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-weight: var(--font-semibold);
}

.stat-card-change.positive {
    color: var(--success-color);
}

.stat-card-change.negative {
    color: var(--danger-color);
}

.stat-card-period {
    color: var(--text-muted);
}

/* ==================== Chart Cards ==================== */

.chart-card {
    background: var(--card-bg);
    border-radius: var(--card-border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    margin-bottom: var(--spacing-lg);
}

.chart-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.chart-card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
}

.chart-card-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.chart-card-body {
    position: relative;
    min-height: 300px;
}

.chart-container {
    position: relative;
    width: 100%;
    height: 100%;
}

/* ==================== Dashboard Grid ==================== */

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.grid-col-12 { grid-column: span 12; }
.grid-col-8 { grid-column: span 8; }
.grid-col-6 { grid-column: span 6; }
.grid-col-4 { grid-column: span 4; }
.grid-col-3 { grid-column: span 3; }

/* ==================== Recent Activity ==================== */

.activity-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.activity-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-secondary);
    border-radius: 50%;
    color: var(--primary-color);
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.activity-description {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin: 0;
}

.activity-time {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    white-space: nowrap;
}

/* ==================== Quick Actions ==================== */

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-fast);
    cursor: pointer;
}

.quick-action-btn:hover {
    border-color: var(--primary-color);
    background-color: var(--bg-secondary);
    transform: translateY(-2px);
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xl);
}

.quick-action-text {
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
    text-align: center;
}

/* ==================== Data Table ==================== */

.data-table-wrapper {
    background: var(--card-bg);
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.data-table-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.data-table-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
}

.data-table-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* ==================== Progress Bars ==================== */

.progress-wrapper {
    margin-bottom: var(--spacing-md);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.progress-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
}

.progress-value {
    font-size: var(--font-size-sm);
    font-weight: var(--font-semibold);
    color: var(--primary-color);
}

.progress-bar-container {
    height: 8px;
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
}

/* ==================== Responsive ==================== */

@media (max-width: 1199.98px) {
    .grid-col-8,
    .grid-col-4 {
        grid-column: span 12;
    }
}

@media (max-width: 767.98px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .grid-col-12,
    .grid-col-8,
    .grid-col-6,
    .grid-col-4,
    .grid-col-3 {
        grid-column: span 1;
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}

