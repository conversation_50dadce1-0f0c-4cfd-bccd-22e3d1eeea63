/**
 * jQuery Demo JavaScript
 * عرض إمكانيات jQuery
 */

$(document).ready(function() {
    console.log('jQuery Demo Page Loaded');
    
    // ========================================
    // Show/Hide Effects
    // ========================================
    
    $('#btnShow').click(function() {
        $('#effectBox').show(500);
    });
    
    $('#btnHide').click(function() {
        $('#effectBox').hide(500);
    });
    
    $('#btnToggle').click(function() {
        $('#effectBox').toggle(500);
    });
    
    $('#btnFadeIn').click(function() {
        $('#effectBox').fadeIn(1000);
    });
    
    $('#btnFadeOut').click(function() {
        $('#effectBox').fadeOut(1000);
    });
    
    $('#btnSlideToggle').click(function() {
        $('#effectBox').slideToggle(500);
    });
    
    // ========================================
    // Animation
    // ========================================
    
    $('#btnAnimateRight').click(function() {
        $('#animBox').animate({
            marginLeft: '200px'
        }, 1000);
    });
    
    $('#btnAnimateLeft').click(function() {
        $('#animBox').animate({
            marginLeft: '0px'
        }, 1000);
    });
    
    $('#btnAnimateSize').click(function() {
        $('#animBox').animate({
            width: '150px',
            height: '150px',
            fontSize: '2rem'
        }, 1000);
    });
    
    $('#btnAnimateReset').click(function() {
        $('#animBox').animate({
            width: '100px',
            height: '100px',
            fontSize: '1rem',
            marginLeft: '0px'
        }, 500);
    });
    
    // ========================================
    // DOM Manipulation
    // ========================================
    
    $('#btnAppend').click(function() {
        var text = $('#inputText').val();
        if (text) {
            $('#contentBox').append('<p class="mb-1"><i class="fas fa-arrow-left text-primary"></i> ' + text + '</p>');
            $('#inputText').val('');
        } else {
            toastr.warning('يرجى إدخال نص أولاً', 'تحذير');
        }
    });
    
    $('#btnPrepend').click(function() {
        var text = $('#inputText').val();
        if (text) {
            $('#contentBox').prepend('<p class="mb-1"><i class="fas fa-arrow-right text-success"></i> ' + text + '</p>');
            $('#inputText').val('');
        } else {
            toastr.warning('يرجى إدخال نص أولاً', 'تحذير');
        }
    });
    
    $('#btnClear').click(function() {
        $('#contentBox').html('<p>المحتوى سيظهر هنا...</p>');
        toastr.info('تم مسح المحتوى', 'معلومة');
    });
    
    // ========================================
    // CSS Manipulation
    // ========================================
    
    $('#btnAddClass').click(function() {
        $('#cssBox').addClass('highlight');
        toastr.success('تمت إضافة Class', 'نجح');
    });
    
    $('#btnRemoveClass').click(function() {
        $('#cssBox').removeClass('highlight');
        toastr.info('تمت إزالة Class', 'معلومة');
    });
    
    $('#btnToggleClass').click(function() {
        $('#cssBox').toggleClass('highlight');
    });
    
    $('#btnChangeCSS').click(function() {
        var randomColor = '#' + Math.floor(Math.random()*16777215).toString(16);
        $('#cssBox').css({
            'background-color': randomColor,
            'color': 'white',
            'border-radius': '20px',
            'transform': 'rotate(5deg)'
        });
        toastr.success('تم تغيير CSS', 'نجح');
    });
    
    // ========================================
    // Event Handling
    // ========================================
    
    var clickCount = 0;
    $('#btnClick').click(function() {
        clickCount++;
        $('#eventLog').prepend('<p class="mb-1"><span class="badge bg-primary">' + new Date().toLocaleTimeString('ar-SA') + '</span> تم الضغط على الزر - المرة رقم ' + clickCount + '</p>');
    });
    
    var dblClickCount = 0;
    $('#btnDblClick').dblclick(function() {
        dblClickCount++;
        $('#eventLog').prepend('<p class="mb-1"><span class="badge bg-success">' + new Date().toLocaleTimeString('ar-SA') + '</span> ضغط مزدوج - المرة رقم ' + dblClickCount + '</p>');
        Swal.fire({
            title: 'ضغط مزدوج!',
            text: 'تم اكتشاف ضغط مزدوج على الزر',
            icon: 'success',
            timer: 2000
        });
    });
    
    $('#hoverBox').hover(
        function() {
            $(this).addClass('highlight');
            $('#eventLog').prepend('<p class="mb-1"><span class="badge bg-info">' + new Date().toLocaleTimeString('ar-SA') + '</span> الماوس دخل المنطقة (Mouse Enter)</p>');
        },
        function() {
            $(this).removeClass('highlight');
            $('#eventLog').prepend('<p class="mb-1"><span class="badge bg-warning">' + new Date().toLocaleTimeString('ar-SA') + '</span> الماوس خرج من المنطقة (Mouse Leave)</p>');
        }
    );
    
    // ========================================
    // AJAX Simulation
    // ========================================
    
    $('#btnLoadData').click(function() {
        var btn = $(this);
        btn.prop('disabled', true);
        btn.html('<i class="fas fa-spinner fa-spin"></i> جاري التحميل...');
        
        $('#ajaxResult').html('<div class="text-center"><i class="fas fa-spinner fa-spin fa-3x text-primary"></i><p class="mt-2">جاري تحميل البيانات...</p></div>');
        
        // محاكاة AJAX
        setTimeout(function() {
            var data = {
                users: [
                    { id: 1, name: 'أحمد محمد', email: '<EMAIL>' },
                    { id: 2, name: 'فاطمة علي', email: '<EMAIL>' },
                    { id: 3, name: 'محمد حسن', email: '<EMAIL>' }
                ]
            };
            
            var html = '<h5 class="text-success"><i class="fas fa-check-circle"></i> تم تحميل البيانات بنجاح</h5>';
            html += '<table class="table table-sm mt-2">';
            html += '<thead><tr><th>#</th><th>الاسم</th><th>البريد</th></tr></thead>';
            html += '<tbody>';
            
            data.users.forEach(function(user) {
                html += '<tr><td>' + user.id + '</td><td>' + user.name + '</td><td>' + user.email + '</td></tr>';
            });
            
            html += '</tbody></table>';
            
            $('#ajaxResult').html(html);
            btn.prop('disabled', false);
            btn.html('<i class="fas fa-download"></i> تحميل بيانات');
            
            toastr.success('تم تحميل ' + data.users.length + ' مستخدمين', 'نجح');
        }, 2000);
    });
    
    $('#btnSaveData').click(function() {
        var btn = $(this);
        btn.prop('disabled', true);
        btn.html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');
        
        $('#ajaxResult').html('<div class="text-center"><i class="fas fa-spinner fa-spin fa-3x text-success"></i><p class="mt-2">جاري حفظ البيانات...</p></div>');
        
        // محاكاة AJAX
        setTimeout(function() {
            $('#ajaxResult').html('<div class="text-center text-success"><i class="fas fa-check-circle fa-3x mb-2"></i><h5>تم الحفظ بنجاح!</h5><p>تم حفظ جميع البيانات في قاعدة البيانات</p></div>');
            
            btn.prop('disabled', false);
            btn.html('<i class="fas fa-save"></i> حفظ بيانات');
            
            Swal.fire({
                icon: 'success',
                title: 'تم الحفظ!',
                text: 'تم حفظ البيانات بنجاح',
                timer: 2000
            });
        }, 1500);
    });
    
    $('#btnRefresh').click(function() {
        var btn = $(this);
        var icon = btn.find('i');
        
        icon.addClass('fa-spin');
        
        setTimeout(function() {
            icon.removeClass('fa-spin');
            $('#ajaxResult').html('<div class="text-center"><i class="fas fa-database fa-3x text-muted mb-2"></i><p>اضغط على الأزرار لمحاكاة عمليات AJAX</p></div>');
            toastr.info('تم التحديث', 'معلومة');
        }, 1000);
    });
    
    // ========================================
    // Chaining
    // ========================================
    
    $('#btnChain').click(function() {
        $('#chainBox')
            .fadeOut(500)
            .fadeIn(500)
            .animate({ marginLeft: '100px' }, 500)
            .animate({ marginLeft: '0px' }, 500)
            .css('background-color', '#10b981')
            .delay(500)
            .css('background-color', '#2563eb')
            .slideUp(500)
            .slideDown(500);
        
        toastr.info('جاري تنفيذ سلسلة من التأثيرات...', 'معلومة');
    });
    
    // ========================================
    // Toastr Configuration
    // ========================================
    
    toastr.options = {
        closeButton: true,
        progressBar: true,
        positionClass: 'toast-top-left',
        timeOut: '3000',
        rtl: true
    };
    
    // رسالة ترحيب
    setTimeout(function() {
        toastr.info('جرب جميع الأزرار لرؤية إمكانيات jQuery!', 'مرحباً');
    }, 1000);
});

