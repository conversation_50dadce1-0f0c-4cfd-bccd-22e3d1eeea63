/**
 * Products Page JavaScript
 * Handles product display, filtering, and view switching
 */

// Sample Products Data
const productsData = [
    { id: 1, name: 'لابتوب Dell XPS 15', category: 'electronics', categoryName: 'إلكترونيات', price: 4500, stock: 15, description: 'لابتوب عالي الأداء مع معالج Intel Core i7', image: 'laptop' },
    { id: 2, name: 'هاتف iPhone 14 Pro', category: 'electronics', categoryName: 'إلكترونيات', price: 5200, stock: 8, description: 'أحدث هاتف من Apple مع كاميرا احترافية', image: 'mobile' },
    { id: 3, name: 'قميص رجالي كلاسيكي', category: 'clothing', categoryName: 'ملابس', price: 150, stock: 45, description: 'قميص قطني عالي الجودة', image: 'shirt' },
    { id: 4, name: 'طاولة مكتب خشبية', category: 'furniture', categoryName: 'أثاث', price: 800, stock: 3, description: 'طاولة مكتب أنيقة من الخشب الطبيعي', image: 'desk' },
    { id: 5, name: 'سماعات Sony WH-1000XM5', category: 'electronics', categoryName: 'إلكترونيات', price: 1200, stock: 20, description: 'سماعات لاسلكية مع إلغاء الضوضاء', image: 'headphones' },
    { id: 6, name: 'بنطلون جينز', category: 'clothing', categoryName: 'ملابس', price: 200, stock: 0, description: 'بنطلون جينز عصري ومريح', image: 'jeans' },
    { id: 7, name: 'أرز بسمتي 5 كجم', category: 'food', categoryName: 'أغذية', price: 45, stock: 100, description: 'أرز بسمتي فاخر من الهند', image: 'rice' },
    { id: 8, name: 'كرسي مكتب مريح', category: 'furniture', categoryName: 'أثاث', price: 650, stock: 12, description: 'كرسي مكتب بتصميم إرجونومي', image: 'chair' },
    { id: 9, name: 'ساعة ذكية Apple Watch', category: 'electronics', categoryName: 'إلكترونيات', price: 1800, stock: 25, description: 'ساعة ذكية مع مراقبة صحية', image: 'watch' },
    { id: 10, name: 'فستان سهرة', category: 'clothing', categoryName: 'ملابس', price: 450, stock: 8, description: 'فستان سهرة أنيق للمناسبات', image: 'dress' },
    { id: 11, name: 'زيت زيتون 1 لتر', category: 'food', categoryName: 'أغذية', price: 65, stock: 50, description: 'زيت زيتون بكر ممتاز', image: 'oil' },
    { id: 12, name: 'خزانة ملابس', category: 'furniture', categoryName: 'أثاث', price: 1500, stock: 5, description: 'خزانة ملابس واسعة بأبواب منزلقة', image: 'wardrobe' },
    { id: 13, name: 'تابلت Samsung Galaxy', category: 'electronics', categoryName: 'إلكترونيات', price: 2200, stock: 15, description: 'تابلت بشاشة كبيرة للعمل والترفيه', image: 'tablet' },
    { id: 14, name: 'حذاء رياضي Nike', category: 'clothing', categoryName: 'ملابس', price: 350, stock: 30, description: 'حذاء رياضي مريح للجري', image: 'shoes' },
    { id: 15, name: 'عسل طبيعي 500 جم', category: 'food', categoryName: 'أغذية', price: 85, stock: 40, description: 'عسل طبيعي صافي 100%', image: 'honey' },
    { id: 16, name: 'كاميرا Canon EOS R6', category: 'electronics', categoryName: 'إلكترونيات', price: 8500, stock: 4, description: 'كاميرا احترافية للتصوير الفوتوغرافي', image: 'camera' },
    { id: 17, name: 'سرير مزدوج', category: 'furniture', categoryName: 'أثاث', price: 2500, stock: 6, description: 'سرير مزدوج مع مرتبة طبية', image: 'bed' },
    { id: 18, name: 'معطف شتوي', category: 'clothing', categoryName: 'ملابس', price: 550, stock: 18, description: 'معطف شتوي دافئ وأنيق', image: 'coat' },
];

// Icon mapping for products
const iconMap = {
    laptop: 'fa-laptop',
    mobile: 'fa-mobile-alt',
    shirt: 'fa-tshirt',
    desk: 'fa-table',
    headphones: 'fa-headphones',
    jeans: 'fa-tshirt',
    rice: 'fa-seedling',
    chair: 'fa-chair',
    watch: 'fa-clock',
    dress: 'fa-tshirt',
    oil: 'fa-wine-bottle',
    wardrobe: 'fa-door-closed',
    tablet: 'fa-tablet-alt',
    shoes: 'fa-shoe-prints',
    honey: 'fa-jar',
    camera: 'fa-camera',
    bed: 'fa-bed',
    coat: 'fa-tshirt'
};

// Current view state
let currentView = 'grid'; // 'grid' or 'table'
let currentPage = 1;
let itemsPerPage = 12;
let filteredProducts = [...productsData];

// Initialize Toastr
toastr.options = {
    closeButton: true,
    progressBar: true,
    positionClass: 'toast-top-left',
    rtl: true,
    timeOut: 3000
};

// Document Ready
$(document).ready(function() {
    // Load products
    loadProducts();
    
    // View toggle buttons
    $('#gridViewBtn').click(function() {
        switchView('grid');
    });
    
    $('#tableViewBtn').click(function() {
        switchView('table');
    });
    
    // Filter buttons
    $('#applyFilters').click(function() {
        applyFilters();
    });
    
    $('#resetFilters').click(function() {
        resetFilters();
    });
    
    // Search input with debounce
    let searchTimeout;
    $('#searchInput').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            applyFilters();
        }, 500);
    });
    
    // Add product button
    $('#btnAddProduct').click(function() {
        showAddProductDialog();
    });
    
    // Export button
    $('#exportBtn').click(function() {
        exportToExcel();
    });
    
    // Update statistics
    updateStatistics();
});

// Switch between grid and table view
function switchView(view) {
    currentView = view;
    
    if (view === 'grid') {
        $('#gridViewBtn').addClass('active');
        $('#tableViewBtn').removeClass('active');
        $('#productsGrid').removeClass('hidden');
        $('#productsTable').addClass('hidden');
    } else {
        $('#tableViewBtn').addClass('active');
        $('#gridViewBtn').removeClass('active');
        $('#productsTable').removeClass('hidden');
        $('#productsGrid').addClass('hidden');
    }
    
    loadProducts();
}

// Load products based on current view
function loadProducts() {
    if (currentView === 'grid') {
        loadProductsGrid();
    } else {
        loadProductsTable();
    }
    
    updatePagination();
}

// Load products in grid view
function loadProductsGrid() {
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    const pageProducts = filteredProducts.slice(start, end);
    
    const grid = $('#productsGrid');
    grid.empty();
    
    pageProducts.forEach(product => {
        const stockStatus = getStockStatus(product.stock);
        const stockClass = stockStatus.class;
        const stockText = stockStatus.text;
        const icon = iconMap[product.image] || 'fa-box';
        
        const card = `
            <div class="product-card" data-product-id="${product.id}">
                <div class="product-image">
                    <i class="fas ${icon}"></i>
                </div>
                <div class="product-body">
                    <span class="product-category">${product.categoryName}</span>
                    <h5 class="product-name">${product.name}</h5>
                    <p class="product-description">${product.description}</p>
                    <div class="product-footer">
                        <div>
                            <div class="product-price">${product.price} ر.س</div>
                            <div class="product-stock ${stockClass}">
                                <i class="fas fa-box"></i> ${stockText}
                            </div>
                        </div>
                    </div>
                    <div class="product-actions">
                        <button class="btn btn-sm btn-primary flex-fill" onclick="editProduct(${product.id})">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteProduct(${product.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        grid.append(card);
    });
}

// Load products in table view
function loadProductsTable() {
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    const pageProducts = filteredProducts.slice(start, end);
    
    const tbody = $('#productsTableBody');
    tbody.empty();
    
    pageProducts.forEach(product => {
        const stockStatus = getStockStatus(product.stock);
        const stockClass = stockStatus.class;
        const stockText = stockStatus.text;
        const icon = iconMap[product.image] || 'fa-box';
        
        const row = `
            <tr data-product-id="${product.id}">
                <td>
                    <div class="product-img-small">
                        <i class="fas ${icon}"></i>
                    </div>
                </td>
                <td><strong>${product.name}</strong></td>
                <td><span class="badge bg-primary">${product.categoryName}</span></td>
                <td><strong>${product.price} ر.س</strong></td>
                <td>${product.stock}</td>
                <td><span class="product-stock ${stockClass}">${stockText}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editProduct(${product.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteProduct(${product.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        
        tbody.append(row);
    });
}

// Get stock status
function getStockStatus(stock) {
    if (stock === 0) {
        return { class: 'out-of-stock', text: 'نفذ من المخزون' };
    } else if (stock < 10) {
        return { class: 'low-stock', text: `مخزون منخفض (${stock})` };
    } else {
        return { class: 'in-stock', text: `متوفر (${stock})` };
    }
}

// Apply filters
function applyFilters() {
    const searchTerm = $('#searchInput').val().toLowerCase();
    const category = $('#categoryFilter').val();
    const stockStatus = $('#stockFilter').val();
    const priceRange = $('#priceFilter').val();
    
    filteredProducts = productsData.filter(product => {
        // Search filter
        const matchesSearch = product.name.toLowerCase().includes(searchTerm) || 
                            product.description.toLowerCase().includes(searchTerm);
        
        // Category filter
        const matchesCategory = !category || product.category === category;
        
        // Stock filter
        let matchesStock = true;
        if (stockStatus === 'in-stock') {
            matchesStock = product.stock >= 10;
        } else if (stockStatus === 'low-stock') {
            matchesStock = product.stock > 0 && product.stock < 10;
        } else if (stockStatus === 'out-of-stock') {
            matchesStock = product.stock === 0;
        }
        
        // Price filter
        let matchesPrice = true;
        if (priceRange) {
            if (priceRange === '0-100') {
                matchesPrice = product.price <= 100;
            } else if (priceRange === '100-500') {
                matchesPrice = product.price > 100 && product.price <= 500;
            } else if (priceRange === '500-1000') {
                matchesPrice = product.price > 500 && product.price <= 1000;
            } else if (priceRange === '1000+') {
                matchesPrice = product.price > 1000;
            }
        }
        
        return matchesSearch && matchesCategory && matchesStock && matchesPrice;
    });
    
    currentPage = 1;
    loadProducts();
    updateStatistics();
    
    toastr.success(`تم العثور على ${filteredProducts.length} منتج`, 'نجح!');
}

// Reset filters
function resetFilters() {
    $('#searchInput').val('');
    $('#categoryFilter').val('');
    $('#stockFilter').val('');
    $('#priceFilter').val('');
    
    filteredProducts = [...productsData];
    currentPage = 1;
    loadProducts();
    updateStatistics();
    
    toastr.info('تم إعادة تعيين الفلاتر', 'معلومة');
}

// Update pagination
function updatePagination() {
    const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
    const start = (currentPage - 1) * itemsPerPage + 1;
    const end = Math.min(currentPage * itemsPerPage, filteredProducts.length);
    
    $('#showingFrom').text(start);
    $('#showingTo').text(end);
    $('#totalItems').text(filteredProducts.length);
    
    const pagination = $('#pagination');
    pagination.empty();
    
    // Previous button
    pagination.append(`
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;">السابق</a>
        </li>
    `);
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
            pagination.append(`
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
                </li>
            `);
        } else if (i === currentPage - 2 || i === currentPage + 2) {
            pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
    }
    
    // Next button
    pagination.append(`
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;">التالي</a>
        </li>
    `);
}

// Change page
function changePage(page) {
    const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        loadProducts();
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}

// Update statistics
function updateStatistics() {
    const total = filteredProducts.length;
    const inStock = filteredProducts.filter(p => p.stock >= 10).length;
    const lowStock = filteredProducts.filter(p => p.stock > 0 && p.stock < 10).length;
    const outOfStock = filteredProducts.filter(p => p.stock === 0).length;
    
    $('#totalProducts').text(total);
    $('#inStockProducts').text(inStock);
    $('#lowStockProducts').text(lowStock);
    $('#outOfStockProducts').text(outOfStock);
}

// Edit product
function editProduct(id) {
    const product = productsData.find(p => p.id === id);
    if (product) {
        Swal.fire({
            title: 'تعديل المنتج',
            html: `
                <div class="text-end">
                    <div class="mb-3">
                        <label class="form-label">اسم المنتج</label>
                        <input type="text" class="form-control" id="editName" value="${product.name}">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">السعر</label>
                        <input type="number" class="form-control" id="editPrice" value="${product.price}">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المخزون</label>
                        <input type="number" class="form-control" id="editStock" value="${product.stock}">
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'حفظ',
            cancelButtonText: 'إلغاء',
            preConfirm: () => {
                product.name = document.getElementById('editName').value;
                product.price = parseFloat(document.getElementById('editPrice').value);
                product.stock = parseInt(document.getElementById('editStock').value);
            }
        }).then((result) => {
            if (result.isConfirmed) {
                loadProducts();
                updateStatistics();
                toastr.success('تم تحديث المنتج بنجاح', 'نجح!');
            }
        });
    }
}

// Delete product
function deleteProduct(id) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: 'لن تتمكن من التراجع عن هذا الإجراء!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#ef4444'
    }).then((result) => {
        if (result.isConfirmed) {
            const index = productsData.findIndex(p => p.id === id);
            if (index > -1) {
                productsData.splice(index, 1);
                filteredProducts = [...productsData];
                loadProducts();
                updateStatistics();
                Swal.fire('تم الحذف!', 'تم حذف المنتج بنجاح', 'success');
            }
        }
    });
}

// Show add product dialog
function showAddProductDialog() {
    Swal.fire({
        title: 'إضافة منتج جديد',
        html: `
            <div class="text-end">
                <div class="mb-3">
                    <label class="form-label">اسم المنتج</label>
                    <input type="text" class="form-control" id="newName" placeholder="أدخل اسم المنتج">
                </div>
                <div class="mb-3">
                    <label class="form-label">الفئة</label>
                    <select class="form-select" id="newCategory">
                        <option value="electronics">إلكترونيات</option>
                        <option value="clothing">ملابس</option>
                        <option value="food">أغذية</option>
                        <option value="furniture">أثاث</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">السعر</label>
                    <input type="number" class="form-control" id="newPrice" placeholder="0.00">
                </div>
                <div class="mb-3">
                    <label class="form-label">المخزون</label>
                    <input type="number" class="form-control" id="newStock" placeholder="0">
                </div>
                <div class="mb-3">
                    <label class="form-label">الوصف</label>
                    <textarea class="form-control" id="newDescription" rows="3"></textarea>
                </div>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'إضافة',
        cancelButtonText: 'إلغاء',
        width: '600px',
        preConfirm: () => {
            const name = document.getElementById('newName').value;
            const category = document.getElementById('newCategory').value;
            const price = parseFloat(document.getElementById('newPrice').value);
            const stock = parseInt(document.getElementById('newStock').value);
            const description = document.getElementById('newDescription').value;
            
            if (!name || !price || !stock) {
                Swal.showValidationMessage('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }
            
            return { name, category, price, stock, description };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            const newProduct = {
                id: productsData.length + 1,
                name: result.value.name,
                category: result.value.category,
                categoryName: getCategoryName(result.value.category),
                price: result.value.price,
                stock: result.value.stock,
                description: result.value.description,
                image: 'box'
            };
            
            productsData.push(newProduct);
            filteredProducts = [...productsData];
            loadProducts();
            updateStatistics();
            
            Swal.fire('تم الإضافة!', 'تم إضافة المنتج بنجاح', 'success');
        }
    });
}

// Get category name
function getCategoryName(category) {
    const categories = {
        electronics: 'إلكترونيات',
        clothing: 'ملابس',
        food: 'أغذية',
        furniture: 'أثاث'
    };
    return categories[category] || category;
}

// Export to Excel (simulation)
function exportToExcel() {
    toastr.success('جاري تصدير البيانات...', 'تصدير');
    
    setTimeout(() => {
        toastr.success('تم تصدير البيانات بنجاح!', 'نجح!');
    }, 1500);
}

