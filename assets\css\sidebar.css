/* 
 * ERP Dashboard - Sidebar Styles
 * أنماط الشريط الجانبي
 */

/* ==================== Sidebar Container ==================== */

.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    overflow-y: auto;
    overflow-x: hidden;
    transition: var(--transition-base);
    z-index: var(--z-fixed);
    box-shadow: var(--shadow-lg);
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* ==================== Sidebar Collapsed State ==================== */

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar.collapsed .sidebar-brand-text,
.sidebar.collapsed .nav-link-text,
.sidebar.collapsed .nav-section-title,
.sidebar.collapsed .badge {
    opacity: 0;
    visibility: hidden;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .sidebar-brand {
    justify-content: center;
    padding: var(--spacing-lg) var(--spacing-sm);
}

.sidebar.collapsed .sidebar-brand-icon {
    margin: 0;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: var(--spacing-md);
    gap: 0;
}

.sidebar.collapsed .nav-link-icon {
    margin: 0;
    width: auto;
}

.sidebar.collapsed .nav-item.has-submenu .nav-link::after {
    display: none;
}

.sidebar.collapsed .nav-section {
    margin-bottom: var(--spacing-md);
}

/* Hide popup by default (for normal sidebar) */
.nav-popup {
    display: none !important;
}

/* Tooltip/Popup for collapsed sidebar */
.sidebar.collapsed .nav-link {
    position: relative;
}

/* Popup menu for collapsed sidebar */
.sidebar.collapsed .nav-link .nav-popup {
    display: none;
    position: absolute;
    right: 100%;
    top: 0;
    background: var(--sidebar-bg);
    color: var(--text-white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    white-space: nowrap;
    margin-left: var(--spacing-sm);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    min-width: 200px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0;
    transform: translateX(10px);
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    pointer-events: none;
}

.sidebar.collapsed .nav-link:hover .nav-popup {
    display: block !important;
    opacity: 1;
    transform: translateX(0);
    pointer-events: auto;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

/* Popup header */
.sidebar.collapsed .nav-popup-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: var(--font-semibold);
    font-size: var(--font-size-base);
}

.sidebar.collapsed .nav-popup-header i {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
}

/* Popup submenu */
.sidebar.collapsed .nav-popup-submenu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar.collapsed .nav-popup-submenu li {
    margin: 0;
}

.sidebar.collapsed .nav-popup-submenu a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--sidebar-text);
    text-decoration: none;
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
}

.sidebar.collapsed .nav-popup-submenu a:hover {
    background: var(--sidebar-hover);
    color: var(--text-white);
    transform: translateX(-3px);
}

.sidebar.collapsed .nav-popup-submenu a i {
    font-size: var(--font-size-sm);
    width: 16px;
    text-align: center;
    opacity: 0.7;
}

/* Arrow indicator for popup */
.sidebar.collapsed .nav-link:hover .nav-popup::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 8px 8px 0;
    border-color: transparent var(--sidebar-bg) transparent transparent;
}

/* Submenu items with has-submenu class */
.sidebar.collapsed .nav-item.has-submenu .nav-link {
    position: relative;
}

.sidebar.collapsed .nav-item.has-submenu .nav-link::after {
    display: none;
}

/* User info in collapsed state */
.sidebar.collapsed .user-info {
    justify-content: center;
    padding: var(--spacing-md);
}

.sidebar.collapsed .user-details {
    display: none;
}

.sidebar.collapsed .user-avatar {
    margin: 0;
}

/* ==================== Sidebar Brand ==================== */

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    min-height: var(--navbar-height);
}

.sidebar-brand-icon {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    flex-shrink: 0;
}

.sidebar-brand-text {
    font-size: var(--font-size-xl);
    font-weight: var(--font-bold);
    color: var(--text-white);
    white-space: nowrap;
    transition: var(--transition-base);
}

/* ==================== Sidebar Navigation ==================== */

.sidebar-nav {
    padding: var(--spacing-lg) 0;
}

.nav-section {
    margin-bottom: var(--spacing-lg);
}

.nav-section-title {
    padding: var(--spacing-sm) var(--spacing-xl);
    font-size: var(--font-size-xs);
    font-weight: var(--font-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--text-muted);
    margin-bottom: var(--spacing-sm);
    transition: var(--transition-base);
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-xl);
    color: var(--sidebar-text);
    text-decoration: none;
    transition: var(--transition-fast);
    position: relative;
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background-color: var(--sidebar-hover);
    color: var(--text-white);
}

.nav-link.active {
    background-color: var(--sidebar-hover);
    color: var(--text-white);
    border-right-color: var(--sidebar-active);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: var(--sidebar-active);
}

.nav-link-icon {
    font-size: var(--font-size-lg);
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

.nav-link-text {
    flex: 1;
    white-space: nowrap;
    transition: var(--transition-base);
}

.nav-link-badge {
    margin-right: auto;
}

/* ==================== Submenu ==================== */

.nav-item.has-submenu .nav-link::after {
    content: '\f078';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: var(--font-size-xs);
    margin-right: auto;
    transition: var(--transition-fast);
}

.nav-item.has-submenu.open .nav-link::after {
    transform: rotate(180deg);
}

.submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-base);
}

.nav-item.has-submenu.open .submenu {
    max-height: 500px;
}

.submenu .nav-link {
    padding-right: calc(var(--spacing-xl) + var(--spacing-lg) + 24px);
    font-size: var(--font-size-sm);
}

.submenu .nav-link::before {
    content: '';
    position: absolute;
    right: calc(var(--spacing-xl) + 12px);
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--text-muted);
    transition: var(--transition-fast);
}

.submenu .nav-link:hover::before,
.submenu .nav-link.active::before {
    background-color: var(--sidebar-active);
}

/* ==================== Sidebar Footer ==================== */

.sidebar-footer {
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background-color: var(--sidebar-bg);
}

.sidebar-user {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
    cursor: pointer;
}

.sidebar-user:hover {
    background-color: var(--sidebar-hover);
}

.sidebar-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
}

.sidebar-user-info {
    flex: 1;
    min-width: 0;
}

.sidebar-user-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-semibold);
    color: var(--text-white);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-user-role {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

/* ==================== Sidebar Toggle Button ==================== */

.sidebar-toggle {
    position: absolute;
    top: calc(var(--navbar-height) / 2 - 20px);
    left: -15px;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: var(--text-white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-fast);
    z-index: 1;
}

.sidebar-toggle:hover {
    background-color: var(--primary-dark);
    transform: scale(1.1);
}

.sidebar-toggle i {
    font-size: var(--font-size-lg);
    transition: var(--transition-fast);
}

.sidebar.collapsed .sidebar-toggle i {
    transform: rotate(180deg);
}

/* ==================== Mobile Responsive ==================== */

@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(100%);
        width: 280px; /* Fixed width for mobile */
        z-index: 1050; /* Higher z-index for mobile overlay */
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .sidebar-toggle {
        display: none;
    }

    /* Improve sidebar brand for mobile */
    .sidebar-brand {
        padding: var(--spacing-md) var(--spacing-lg);
        min-height: var(--navbar-height);
    }

    .sidebar-brand-text {
        font-size: var(--font-size-lg);
    }

    /* Improve navigation for mobile */
    .nav-link {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
    }

    .nav-link-icon {
        font-size: var(--font-size-lg);
        width: 20px;
    }

    .nav-section-title {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-xs);
    }

    /* Improve submenu for mobile */
    .submenu .nav-link {
        padding-right: calc(var(--spacing-lg) + var(--spacing-xl));
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 767.98px) {
    .sidebar {
        width: 100vw; /* Full width on small mobile */
        max-width: 320px; /* But not too wide */
    }

    /* Reduce padding for small screens */
    .sidebar-brand {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .nav-link {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .nav-section-title {
        padding: var(--spacing-xs) var(--spacing-md);
    }

    /* Make touch targets larger */
    .nav-link {
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    /* Improve badge visibility */
    .nav-link-badge {
        font-size: var(--font-size-xs);
        padding: 0.125rem 0.5rem;
    }
}

@media (max-width: 575.98px) {
    .sidebar {
        width: 100vw;
        max-width: none;
    }

    /* Further reduce spacing for very small screens */
    .sidebar-nav {
        padding: var(--spacing-md) 0;
    }

    .nav-section {
        margin-bottom: var(--spacing-md);
    }
}

/* ==================== Sidebar Overlay (Mobile) ==================== */

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: calc(var(--z-fixed) - 1);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-base);
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

@media (min-width: 992px) {
    .sidebar-overlay {
        display: none;
    }
}

