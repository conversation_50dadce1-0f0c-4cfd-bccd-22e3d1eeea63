/* 
 * ERP Dashboard - Navbar Styles
 * أنماط شريط التنقل العلوي
 */

/* ==================== Navbar Container ==================== */

.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: var(--sidebar-width);
    height: var(--navbar-height);
    background-color: var(--navbar-bg);
    border-bottom: 1px solid var(--navbar-border);
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-xl);
    z-index: var(--z-sticky);
    transition: var(--transition-base);
    box-shadow: var(--shadow-sm);
}

.navbar.sidebar-collapsed {
    right: var(--sidebar-collapsed-width);
}

/* ==================== Navbar Left Section ==================== */

.navbar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.navbar-menu-toggle {
    display: none;
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.navbar-menu-toggle:hover {
    background-color: var(--bg-secondary);
}

.navbar-search {
    position: relative;
    width: 400px;
}

.navbar-search-input {
    width: 100%;
    padding: 0.625rem 1rem 0.625rem 3rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

.navbar-search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: var(--bg-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.navbar-search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
}

/* ==================== Navbar Right Section ==================== */

.navbar-right {
    margin-right: auto;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* ==================== Navbar Actions ==================== */

.navbar-action {
    position: relative;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.navbar-action:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.navbar-action-badge {
    position: absolute;
    top: 4px;
    left: 4px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--danger-color);
    color: white;
    font-size: 10px;
    font-weight: var(--font-bold);
    border-radius: var(--radius-full);
    padding: 0 4px;
}

/* ==================== Navbar Dropdown ==================== */

.navbar-dropdown {
    position: relative;
}

.navbar-dropdown-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: 0.5rem 1rem;
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.navbar-dropdown-toggle:hover {
    background-color: var(--bg-secondary);
}

.navbar-dropdown-menu {
    position: absolute;
    top: calc(100% + 0.5rem);
    left: 0;
    min-width: 280px;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-fast);
    z-index: var(--z-dropdown);
    max-height: 400px;
    overflow-y: auto;
}

.navbar-dropdown.show .navbar-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.navbar-dropdown-header {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.navbar-dropdown-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
}

.navbar-dropdown-body {
    padding: var(--spacing-sm) 0;
}

.navbar-dropdown-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-fast);
    border-right: 3px solid transparent;
}

.navbar-dropdown-item:hover {
    background-color: var(--bg-secondary);
    border-right-color: var(--primary-color);
}

.navbar-dropdown-item-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    color: var(--primary-color);
    flex-shrink: 0;
}

.navbar-dropdown-item-content {
    flex: 1;
    min-width: 0;
}

.navbar-dropdown-item-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.navbar-dropdown-item-text {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.navbar-dropdown-item-time {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    white-space: nowrap;
}

.navbar-dropdown-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.navbar-dropdown-footer a {
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
    color: var(--primary-color);
    text-decoration: none;
}

.navbar-dropdown-footer a:hover {
    text-decoration: underline;
}

/* ==================== User Dropdown ==================== */

.navbar-user {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.navbar-user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
}

.navbar-user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.navbar-user-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
    line-height: 1.2;
}

.navbar-user-role {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    line-height: 1.2;
}

.navbar-user-dropdown .navbar-dropdown-menu {
    left: auto;
    right: 0;
}

/* ==================== Breadcrumb ==================== */

.navbar-breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
}

.breadcrumb-item a {
    color: var(--text-secondary);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--text-primary);
    font-weight: var(--font-medium);
}

.breadcrumb-separator {
    color: var(--text-muted);
}

/* ==================== Language Toggle ==================== */

.language-dropdown-menu {
    min-width: 280px;
}

.language-option {
    position: relative;
}

.language-check {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    opacity: 0;
    transition: var(--transition-fast);
}

.language-option.active .language-check {
    opacity: 1;
}

.language-text {
    margin-right: 0.5rem;
    font-size: var(--font-size-sm);
    font-weight: var(--font-medium);
}

/* ==================== Mobile Responsive ==================== */

@media (max-width: 991.98px) {
    .navbar {
        right: 0 !important;
        left: 0 !important;
        padding: 0 var(--spacing-lg);
    }

    .navbar.sidebar-collapsed {
        right: 0 !important;
        left: 0 !important;
    }

    .navbar-menu-toggle {
        display: flex;
        order: -1; /* Move to the beginning */
    }

    .navbar-search {
        display: none;
    }

    .language-text {
        display: none;
    }

    /* Improve navbar left section */
    .navbar-left {
        gap: var(--spacing-md);
        flex: 1;
    }

    /* Improve navbar actions */
    .navbar-action {
        width: 36px;
        height: 36px;
        font-size: var(--font-size-base);
    }

    .navbar-action-badge {
        top: 2px;
        left: 2px;
        min-width: 16px;
        height: 16px;
        font-size: 9px;
    }

    /* Improve dropdown positioning */
    .navbar-dropdown-menu {
        min-width: 260px;
        max-width: calc(100vw - 2rem);
        right: 0;
        left: auto;
    }
}

@media (max-width: 767.98px) {
    .navbar {
        padding: 0 var(--spacing-md);
        height: calc(var(--navbar-height) - 4px); /* Slightly shorter on mobile */
    }

    .navbar-user-info {
        display: none;
    }

    .navbar-right {
        gap: var(--spacing-xs);
    }

    /* Make navbar actions touch-friendly */
    .navbar-action {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-lg);
    }

    .navbar-menu-toggle {
        width: 44px;
        height: 44px;
        font-size: var(--font-size-xl);
    }

    /* Improve dropdown for mobile */
    .navbar-dropdown-menu {
        min-width: 240px;
        max-width: calc(100vw - 1rem);
        max-height: calc(100vh - var(--navbar-height) - 2rem);
    }

    .navbar-dropdown-item {
        padding: var(--spacing-md) var(--spacing-lg);
        min-height: 44px; /* Touch-friendly height */
    }

    .navbar-dropdown-item-icon {
        width: 36px;
        height: 36px;
    }

    /* User dropdown improvements */
    .navbar-user-dropdown .navbar-dropdown-menu {
        right: var(--spacing-md);
        left: auto;
    }
}

@media (max-width: 575.98px) {
    .navbar {
        padding: 0 var(--spacing-sm);
    }

    .navbar-left {
        gap: var(--spacing-sm);
    }

    .navbar-right {
        gap: 0.25rem;
    }

    /* Further reduce action button sizes for very small screens */
    .navbar-action {
        width: 36px;
        height: 36px;
        font-size: var(--font-size-base);
    }

    .navbar-menu-toggle {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-lg);
    }

    /* Full width dropdown on very small screens */
    .navbar-dropdown-menu {
        right: var(--spacing-sm);
        left: var(--spacing-sm);
        min-width: auto;
        max-width: none;
        width: calc(100vw - 2 * var(--spacing-sm));
    }
}

