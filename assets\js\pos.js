/**
 * POS (Point of Sale) System
 * نظام نقطة البيع
 */

// Configure Toastr
toastr.options = {
    "closeButton": true,
    "progressBar": true,
    "positionClass": "toast-top-left",
    "timeOut": "3000",
    "rtl": true
};

// Sample Products Data
const posProducts = [
    { id: 1, name: 'لابتوب Dell XPS 15', category: 'electronics', price: 4500, stock: 15, icon: 'fa-laptop' },
    { id: 2, name: 'آيفون 15 برو', category: 'electronics', price: 5200, stock: 8, icon: 'fa-mobile-alt' },
    { id: 3, name: 'سماعات AirPods Pro', category: 'electronics', price: 950, stock: 25, icon: 'fa-headphones' },
    { id: 4, name: 'ساعة Apple Watch', category: 'electronics', price: 1800, stock: 12, icon: 'fa-clock' },
    { id: 5, name: 'كاميرا Canon EOS', category: 'electronics', price: 3200, stock: 6, icon: 'fa-camera' },
    { id: 6, name: 'تيشيرت قطن', category: 'clothing', price: 85, stock: 50, icon: 'fa-tshirt' },
    { id: 7, name: 'بنطلون جينز', category: 'clothing', price: 180, stock: 30, icon: 'fa-user' },
    { id: 8, name: 'حذاء رياضي Nike', category: 'clothing', price: 450, stock: 20, icon: 'fa-shoe-prints' },
    { id: 9, name: 'قهوة عربية فاخرة', category: 'food', price: 45, stock: 100, icon: 'fa-coffee' },
    { id: 10, name: 'شاي أخضر', category: 'food', price: 25, stock: 150, icon: 'fa-mug-hot' },
    { id: 11, name: 'عسل طبيعي', category: 'food', price: 120, stock: 40, icon: 'fa-jar' },
    { id: 12, name: 'كرسي مكتب', category: 'furniture', price: 650, stock: 10, icon: 'fa-chair' },
    { id: 13, name: 'طاولة خشبية', category: 'furniture', price: 1200, stock: 5, icon: 'fa-table' },
    { id: 14, name: 'مكتبة كتب', category: 'furniture', price: 850, stock: 8, icon: 'fa-book' },
    { id: 15, name: 'ماوس لاسلكي', category: 'electronics', price: 95, stock: 45, icon: 'fa-mouse' },
    { id: 16, name: 'لوحة مفاتيح ميكانيكية', category: 'electronics', price: 380, stock: 18, icon: 'fa-keyboard' },
    { id: 17, name: 'شاشة 27 بوصة', category: 'electronics', price: 1100, stock: 12, icon: 'fa-desktop' },
    { id: 18, name: 'طابعة HP', category: 'electronics', price: 550, stock: 9, icon: 'fa-print' }
];

// Cart Data
let cart = [];
let currentCategory = 'all';

// Initialize
$(document).ready(function() {
    loadProducts();
    updateCart();
    updateClock();
    setInterval(updateClock, 1000);
    
    // Category buttons
    $('.category-btn').click(function() {
        $('.category-btn').removeClass('active');
        $(this).addClass('active');
        currentCategory = $(this).data('category');
        loadProducts();
    });
    
    // Search
    $('#posSearch').on('input', function() {
        loadProducts();
    });
    
    // Keyboard shortcuts
    $(document).keydown(function(e) {
        // F2 - Focus search
        if (e.key === 'F2') {
            e.preventDefault();
            $('#posSearch').focus();
        }
        // F12 - Complete order
        if (e.key === 'F12') {
            e.preventDefault();
            if (cart.length > 0) {
                completeOrder();
            }
        }
        // ESC - Clear search
        if (e.key === 'Escape') {
            $('#posSearch').val('').trigger('input');
        }
    });
});

// Update Clock
function updateClock() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', { 
        hour: '2-digit', 
        minute: '2-digit',
        second: '2-digit'
    });
    $('#currentTime').text(timeString);
}

// Load Products
function loadProducts() {
    const searchTerm = $('#posSearch').val().toLowerCase();
    let filteredProducts = posProducts;
    
    // Filter by category
    if (currentCategory !== 'all') {
        filteredProducts = filteredProducts.filter(p => p.category === currentCategory);
    }
    
    // Filter by search
    if (searchTerm) {
        filteredProducts = filteredProducts.filter(p => 
            p.name.toLowerCase().includes(searchTerm)
        );
    }
    
    // Render products
    const grid = $('#posProductsGrid');
    grid.empty();
    
    if (filteredProducts.length === 0) {
        grid.html(`
            <div style="grid-column: 1/-1; text-align: center; padding: 3rem; color: #9ca3af;">
                <i class="fas fa-box-open" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                <p>لا توجد منتجات</p>
            </div>
        `);
        return;
    }
    
    filteredProducts.forEach(product => {
        const stockClass = product.stock < 10 ? 'text-danger' : product.stock < 20 ? 'text-warning' : 'text-success';
        const card = $(`
            <div class="pos-product-card" onclick="addToCart(${product.id})">
                <div class="pos-product-icon">
                    <i class="fas ${product.icon}"></i>
                </div>
                <div class="pos-product-name">${product.name}</div>
                <div class="pos-product-price">${product.price.toFixed(2)} ر.س</div>
                <div class="pos-product-stock ${stockClass}">
                    <i class="fas fa-box"></i> ${product.stock}
                </div>
            </div>
        `);
        grid.append(card);
    });
}

// Add to Cart
function addToCart(productId) {
    const product = posProducts.find(p => p.id === productId);
    if (!product) return;
    
    // Check stock
    const cartItem = cart.find(item => item.id === productId);
    const currentQty = cartItem ? cartItem.quantity : 0;
    
    if (currentQty >= product.stock) {
        toastr.warning('المخزون غير كافي', 'تنبيه');
        return;
    }
    
    // Add or update cart
    if (cartItem) {
        cartItem.quantity++;
    } else {
        cart.push({
            id: product.id,
            name: product.name,
            price: product.price,
            quantity: 1,
            icon: product.icon
        });
    }
    
    updateCart();
    toastr.success(`تم إضافة ${product.name}`, 'نجح');
    
    // Play sound (optional)
    playBeep();
}

// Update Cart
function updateCart() {
    const cartContainer = $('#posCartItems');
    
    if (cart.length === 0) {
        cartContainer.html(`
            <div class="pos-cart-empty">
                <i class="fas fa-shopping-cart"></i>
                <p>السلة فارغة</p>
                <small>أضف منتجات لبدء البيع</small>
            </div>
        `);
        $('#cartCount').text('0');
        updateSummary();
        return;
    }
    
    cartContainer.empty();
    
    cart.forEach((item, index) => {
        const itemTotal = item.price * item.quantity;
        const cartItem = $(`
            <div class="pos-cart-item">
                <div class="pos-cart-item-icon">
                    <i class="fas ${item.icon}"></i>
                </div>
                <div class="pos-cart-item-details">
                    <div class="pos-cart-item-name">${item.name}</div>
                    <div class="pos-cart-item-price">${item.price.toFixed(2)} ر.س</div>
                </div>
                <div class="pos-cart-item-quantity">
                    <button class="qty-btn" onclick="decreaseQuantity(${index})">
                        <i class="fas fa-minus"></i>
                    </button>
                    <span class="qty-value">${item.quantity}</span>
                    <button class="qty-btn" onclick="increaseQuantity(${index})">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <div class="pos-cart-item-total">${itemTotal.toFixed(2)} ر.س</div>
                <button class="pos-cart-item-remove" onclick="removeFromCart(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `);
        cartContainer.append(cartItem);
    });
    
    $('#cartCount').text(cart.length);
    updateSummary();
}

// Increase Quantity
function increaseQuantity(index) {
    const item = cart[index];
    const product = posProducts.find(p => p.id === item.id);
    
    if (item.quantity >= product.stock) {
        toastr.warning('المخزون غير كافي', 'تنبيه');
        return;
    }
    
    item.quantity++;
    updateCart();
}

// Decrease Quantity
function decreaseQuantity(index) {
    const item = cart[index];
    
    if (item.quantity > 1) {
        item.quantity--;
        updateCart();
    } else {
        removeFromCart(index);
    }
}

// Remove from Cart
function removeFromCart(index) {
    const item = cart[index];
    
    Swal.fire({
        title: 'حذف المنتج',
        text: `هل تريد حذف ${item.name} من السلة؟`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#ef4444'
    }).then((result) => {
        if (result.isConfirmed) {
            cart.splice(index, 1);
            updateCart();
            toastr.success('تم حذف المنتج', 'نجح');
        }
    });
}

// Update Summary
function updateSummary() {
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const taxRate = 0.15;
    const tax = subtotal * taxRate;
    const discount = 0; // Can be implemented
    const total = subtotal + tax - discount;
    
    $('#subtotal').text(subtotal.toFixed(2) + ' ر.س');
    $('#tax').text(tax.toFixed(2) + ' ر.س');
    $('#discount').text(discount.toFixed(2) + ' ر.س');
    $('#total').text(total.toFixed(2) + ' ر.س');
}

// Clear Cart
function clearCart() {
    if (cart.length === 0) {
        toastr.info('السلة فارغة بالفعل', 'معلومة');
        return;
    }
    
    Swal.fire({
        title: 'مسح السلة',
        text: 'هل تريد مسح جميع المنتجات من السلة؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، امسح',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#ef4444'
    }).then((result) => {
        if (result.isConfirmed) {
            cart = [];
            updateCart();
            toastr.success('تم مسح السلة', 'نجح');
        }
    });
}

// Hold Order
function holdOrder() {
    if (cart.length === 0) {
        toastr.warning('السلة فارغة', 'تنبيه');
        return;
    }
    
    // Save to localStorage
    const heldOrders = JSON.parse(localStorage.getItem('heldOrders') || '[]');
    heldOrders.push({
        id: Date.now(),
        date: new Date().toISOString(),
        items: [...cart],
        customer: $('#customerSelect option:selected').text()
    });
    localStorage.setItem('heldOrders', JSON.stringify(heldOrders));
    
    cart = [];
    updateCart();
    toastr.success('تم تعليق الطلب', 'نجح');
}

// Complete Order
function completeOrder() {
    if (cart.length === 0) {
        toastr.warning('السلة فارغة', 'تنبيه');
        return;
    }
    
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const tax = subtotal * 0.15;
    const total = subtotal + tax;
    
    Swal.fire({
        title: 'إتمام عملية البيع',
        html: `
            <div style="text-align: right;">
                <h4 style="margin-bottom: 1rem;">طريقة الدفع:</h4>
                <div style="display: grid; gap: 1rem;">
                    <button class="swal2-confirm swal2-styled" onclick="processPayment('cash')" style="width: 100%;">
                        <i class="fas fa-money-bill-wave"></i> نقداً
                    </button>
                    <button class="swal2-confirm swal2-styled" onclick="processPayment('card')" style="width: 100%; background: #10b981;">
                        <i class="fas fa-credit-card"></i> بطاقة
                    </button>
                    <button class="swal2-confirm swal2-styled" onclick="processPayment('transfer')" style="width: 100%; background: #f59e0b;">
                        <i class="fas fa-exchange-alt"></i> تحويل
                    </button>
                </div>
                <div style="margin-top: 1.5rem; padding-top: 1rem; border-top: 2px solid #e5e7eb;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>المجموع:</span>
                        <strong>${total.toFixed(2)} ر.س</strong>
                    </div>
                </div>
            </div>
        `,
        showConfirmButton: false,
        showCancelButton: true,
        cancelButtonText: 'إلغاء',
        width: '500px'
    });
}

// Process Payment
function processPayment(method) {
    const methodNames = {
        'cash': 'نقداً',
        'card': 'بطاقة',
        'transfer': 'تحويل'
    };
    
    Swal.close();
    
    // Show success
    Swal.fire({
        title: 'تمت العملية بنجاح!',
        html: `
            <div style="text-align: center;">
                <i class="fas fa-check-circle" style="font-size: 4rem; color: #10b981; margin-bottom: 1rem;"></i>
                <p style="font-size: 1.25rem; margin-bottom: 1rem;">تم الدفع ${methodNames[method]}</p>
                <div style="background: #f3f4f6; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>رقم الفاتورة:</span>
                        <strong>INV-${Date.now()}</strong>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>المبلغ المدفوع:</span>
                        <strong>${$('#total').text()}</strong>
                    </div>
                </div>
            </div>
        `,
        icon: 'success',
        showCancelButton: true,
        confirmButtonText: '<i class="fas fa-print"></i> طباعة الفاتورة',
        cancelButtonText: 'إغلاق',
        confirmButtonColor: '#6366f1'
    }).then((result) => {
        if (result.isConfirmed) {
            printReceipt();
        }
    });
    
    // Clear cart
    cart = [];
    updateCart();
    $('#customerSelect').val('');
    
    toastr.success('تمت عملية البيع بنجاح', 'نجح');
}

// Print Receipt
function printReceipt() {
    toastr.info('جاري طباعة الفاتورة...', 'طباعة');
    // Here you can implement actual printing logic
}

// Play Beep Sound
function playBeep() {
    // Optional: Add beep sound
    // const audio = new Audio('beep.mp3');
    // audio.play();
}

// Show All Products
function showAllProducts() {
    currentCategory = 'all';
    $('.category-btn').removeClass('active');
    $('.category-btn[data-category="all"]').addClass('active');
    $('#posSearch').val('');
    loadProducts();
}

// Show Favorites
function showFavorites() {
    toastr.info('ميزة المفضلة قيد التطوير', 'معلومة');
}

// Show Recent
function showRecent() {
    toastr.info('ميزة الأخيرة قيد التطوير', 'معلومة');
}

