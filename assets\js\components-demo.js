/**
 * Components Demo JavaScript
 * عرض جميع المكونات والمكتبات
 */

// ========================================
// Bootstrap Modal Animations
// ========================================

// Show modal with custom animation
function showModalWithAnimation(modalId, animationType) {
    const modal = document.getElementById(modalId);
    const modalElement = new bootstrap.Modal(modal);

    // Add animation class
    modal.classList.add(animationType);

    // Show modal
    modalElement.show();

    // Remove animation class after modal is shown
    modal.addEventListener('shown.bs.modal', function() {
        setTimeout(() => {
            modal.classList.remove(animationType);
        }, 500);
    }, { once: true });
}

// ========================================
// SweetAlert2 Functions
// ========================================

// تنبيه أساسي
function showBasicAlert() {
    Swal.fire({
        title: 'مرحباً!',
        text: 'هذا تنبيه أساسي من SweetAlert2',
        icon: 'info',
        confirmButtonText: 'حسناً'
    });
}

// تنبيه نجاح
function showSuccessAlert() {
    Swal.fire({
        title: 'ممتاز!',
        text: 'تمت العملية بنجاح',
        icon: 'success',
        confirmButtonText: 'رائع!',
        timer: 3000
    });
}

// تنبيه خطأ
function showErrorAlert() {
    Swal.fire({
        title: 'خطأ!',
        text: 'حدث خطأ أثناء تنفيذ العملية',
        icon: 'error',
        confirmButtonText: 'حسناً',
        confirmButtonColor: '#ef4444'
    });
}

// تنبيه تحذير
function showWarningAlert() {
    Swal.fire({
        title: 'تحذير!',
        text: 'يجب الانتباه لهذا الأمر',
        icon: 'warning',
        confirmButtonText: 'فهمت',
        confirmButtonColor: '#f59e0b'
    });
}

// تنبيه سؤال
function showQuestionAlert() {
    Swal.fire({
        title: 'هل لديك سؤال؟',
        text: 'يمكنك طرح أي سؤال هنا',
        icon: 'question',
        confirmButtonText: 'نعم',
        showCancelButton: true,
        cancelButtonText: 'لا'
    });
}

// تنبيه تأكيد
function showConfirmAlert() {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: 'لن تتمكن من التراجع عن هذا الإجراء!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#10b981',
        cancelButtonColor: '#ef4444',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'تم الحذف!',
                text: 'تم حذف العنصر بنجاح.',
                icon: 'success',
                confirmButtonText: 'حسناً'
            });
        }
    });
}

// تنبيه مع إدخال نص
function showInputAlert() {
    Swal.fire({
        title: 'أدخل اسمك',
        input: 'text',
        inputPlaceholder: 'اكتب اسمك هنا...',
        showCancelButton: true,
        confirmButtonText: 'إرسال',
        cancelButtonText: 'إلغاء',
        inputValidator: (value) => {
            if (!value) {
                return 'يجب إدخال اسم!';
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: `مرحباً ${result.value}!`,
                text: 'سعداء بلقائك',
                icon: 'success'
            });
        }
    });
}

// تنبيه مع مؤقت
function showTimerAlert() {
    let timerInterval;
    Swal.fire({
        title: 'سيتم الإغلاق تلقائياً!',
        html: 'سيتم إغلاق هذه النافذة خلال <b></b> ثانية.',
        timer: 5000,
        timerProgressBar: true,
        didOpen: () => {
            Swal.showLoading();
            const b = Swal.getHtmlContainer().querySelector('b');
            timerInterval = setInterval(() => {
                b.textContent = Math.ceil(Swal.getTimerLeft() / 1000);
            }, 100);
        },
        willClose: () => {
            clearInterval(timerInterval);
        }
    }).then((result) => {
        if (result.dismiss === Swal.DismissReason.timer) {
            console.log('تم الإغلاق بواسطة المؤقت');
        }
    });
}

// ========================================
// Toastr Functions
// ========================================

// إعدادات Toastr
toastr.options = {
    closeButton: true,
    debug: false,
    newestOnTop: true,
    progressBar: true,
    positionClass: 'toast-top-left',
    preventDuplicates: false,
    onclick: null,
    showDuration: '300',
    hideDuration: '1000',
    timeOut: '5000',
    extendedTimeOut: '1000',
    showEasing: 'swing',
    hideEasing: 'linear',
    showMethod: 'fadeIn',
    hideMethod: 'fadeOut',
    rtl: true
};

// Toast نجاح
function showToastSuccess() {
    toastr.success('تمت العملية بنجاح!', 'نجح!');
}

// Toast خطأ
function showToastError() {
    toastr.error('حدث خطأ أثناء تنفيذ العملية', 'خطأ!');
}

// Toast تحذير
function showToastWarning() {
    toastr.warning('يرجى التحقق من البيانات المدخلة', 'تحذير!');
}

// Toast معلومة
function showToastInfo() {
    toastr.info('لديك 5 رسائل جديدة', 'معلومة');
}

// ========================================
// Chart.js Demo
// ========================================

// إعدادات Chart.js الافتراضية
Chart.defaults.font.family = 'Cairo, sans-serif';
Chart.defaults.plugins.legend.rtl = true;
Chart.defaults.plugins.legend.textDirection = 'rtl';
Chart.defaults.plugins.tooltip.rtl = true;
Chart.defaults.plugins.tooltip.textDirection = 'rtl';

// رسم بياني خطي
function createLineChart() {
    const ctx = document.getElementById('lineChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'المبيعات',
                data: [12, 19, 3, 5, 2, 3],
                borderColor: 'rgb(37, 99, 235)',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// رسم بياني شريطي
function createBarChart() {
    const ctx = document.getElementById('barChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['المنتج أ', 'المنتج ب', 'المنتج ج', 'المنتج د', 'المنتج هـ'],
            datasets: [{
                label: 'المبيعات',
                data: [65, 59, 80, 81, 56],
                backgroundColor: [
                    'rgba(37, 99, 235, 0.8)',
                    'rgba(16, 185, 129, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(239, 68, 68, 0.8)',
                    'rgba(139, 92, 246, 0.8)'
                ],
                borderColor: [
                    'rgb(37, 99, 235)',
                    'rgb(16, 185, 129)',
                    'rgb(245, 158, 11)',
                    'rgb(239, 68, 68)',
                    'rgb(139, 92, 246)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// رسم بياني دائري
function createPieChart() {
    const ctx = document.getElementById('pieChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['المبيعات', 'التسويق', 'التطوير', 'الدعم'],
            datasets: [{
                data: [300, 50, 100, 80],
                backgroundColor: [
                    'rgba(37, 99, 235, 0.8)',
                    'rgba(16, 185, 129, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(239, 68, 68, 0.8)'
                ],
                borderColor: [
                    'rgb(37, 99, 235)',
                    'rgb(16, 185, 129)',
                    'rgb(245, 158, 11)',
                    'rgb(239, 68, 68)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
}

// رسم بياني دائري مفرغ
function createDoughnutChart() {
    const ctx = document.getElementById('doughnutChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['مكتمل', 'قيد التنفيذ', 'معلق', 'ملغي'],
            datasets: [{
                data: [55, 25, 15, 5],
                backgroundColor: [
                    'rgba(16, 185, 129, 0.8)',
                    'rgba(37, 99, 235, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(239, 68, 68, 0.8)'
                ],
                borderColor: [
                    'rgb(16, 185, 129)',
                    'rgb(37, 99, 235)',
                    'rgb(245, 158, 11)',
                    'rgb(239, 68, 68)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// ========================================
// Initialize on Page Load
// ========================================

$(document).ready(function() {
    console.log('Components Demo Page Loaded');
    
    // إنشاء الرسوم البيانية
    createLineChart();
    createBarChart();
    createPieChart();
    createDoughnutChart();
    
    // رسالة ترحيب
    setTimeout(function() {
        toastr.info('مرحباً بك في صفحة عرض المكونات!', 'مرحباً');
    }, 1000);
});

