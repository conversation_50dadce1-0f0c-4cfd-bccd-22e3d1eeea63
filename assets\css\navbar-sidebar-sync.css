/* 
 * ERP Dashboard - Navbar & Sidebar Synchronization
 * ضمان التناسق المثالي بين شريط التنقل والشريط الجانبي
 */

/* ==================== General Synchronization ==================== */

/* Ensure consistent heights across all breakpoints */
.navbar,
.sidebar-brand {
    height: var(--navbar-height);
    min-height: var(--navbar-height);
}

/* ==================== Desktop Synchronization ==================== */

@media (min-width: 992px) {
    /* Ensure sidebar toggle button aligns with navbar */
    .sidebar-toggle {
        top: calc(var(--navbar-height) / 2 - 20px);
    }
    
    /* Ensure consistent spacing */
    .sidebar-brand {
        padding: var(--spacing-lg) var(--spacing-xl);
    }
    
    .navbar {
        padding: 0 var(--spacing-xl);
    }
}

/* ==================== Tablet Synchronization (991.98px) ==================== */

@media (max-width: 991.98px) {
    /* Ensure navbar and sidebar brand have same height */
    .navbar,
    .sidebar-brand {
        height: var(--navbar-height);
        min-height: var(--navbar-height);
    }
    
    /* Consistent padding */
    .navbar {
        padding: 0 var(--spacing-lg);
    }
    
    .sidebar-brand {
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    /* Ensure menu toggle is properly aligned */
    .navbar-menu-toggle {
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* Sidebar brand icon and text alignment */
    .sidebar-brand {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .sidebar-brand-icon {
        font-size: var(--font-size-xl);
        flex-shrink: 0;
    }
    
    .sidebar-brand-text {
        font-size: var(--font-size-lg);
        line-height: 1.2;
    }
}

/* ==================== Mobile Synchronization (767.98px) ==================== */

@media (max-width: 767.98px) {
    /* Critical: Ensure exact height matching */
    .navbar,
    .sidebar-brand {
        height: var(--navbar-height) !important;
        min-height: var(--navbar-height) !important;
        max-height: var(--navbar-height) !important;
    }
    
    /* Consistent padding and alignment */
    .navbar {
        padding: 0 var(--spacing-md);
        display: flex;
        align-items: center;
    }
    
    .sidebar-brand {
        padding: var(--spacing-md);
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    /* Menu toggle button - exact sizing */
    .navbar-menu-toggle {
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }
    
    /* Sidebar brand components */
    .sidebar-brand-icon {
        font-size: var(--font-size-xl);
        flex-shrink: 0;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .sidebar-brand-text {
        font-size: var(--font-size-lg);
        line-height: 1.2;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    /* Navbar left section alignment */
    .navbar-left {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        height: 100%;
    }
    
    /* Navbar right section alignment */
    .navbar-right {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        height: 100%;
    }
    
    /* Navbar actions consistent sizing */
    .navbar-action {
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }
}

/* ==================== Small Mobile Synchronization (575.98px) ==================== */

@media (max-width: 575.98px) {
    /* Maintain height consistency even on very small screens */
    .navbar,
    .sidebar-brand {
        height: var(--navbar-height) !important;
        min-height: var(--navbar-height) !important;
    }
    
    /* Reduce padding but maintain alignment */
    .navbar {
        padding: 0 var(--spacing-sm);
    }
    
    .sidebar-brand {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    /* Slightly smaller icons but maintain proportions */
    .sidebar-brand-icon {
        font-size: var(--font-size-lg);
        width: 28px;
        height: 28px;
    }
    
    .sidebar-brand-text {
        font-size: var(--font-size-base);
    }
    
    /* Maintain button sizes for touch accessibility */
    .navbar-menu-toggle,
    .navbar-action {
        width: 40px;
        height: 40px;
    }
}

/* ==================== RTL/LTR Synchronization ==================== */

/* RTL specific alignments */
[dir="rtl"] .sidebar-brand {
    flex-direction: row;
    text-align: right;
}

[dir="rtl"] .navbar-left {
    flex-direction: row;
}

/* LTR specific alignments */
[dir="ltr"] .sidebar-brand {
    flex-direction: row;
    text-align: left;
}

[dir="ltr"] .navbar-left {
    flex-direction: row;
}

/* ==================== Animation Synchronization ==================== */

/* Ensure smooth transitions maintain alignment */
.navbar,
.sidebar-brand,
.navbar-menu-toggle,
.navbar-action {
    transition: all var(--transition-fast);
}

/* Prevent layout shifts during animations */
.navbar-menu-toggle,
.navbar-action,
.sidebar-brand-icon {
    transform-origin: center;
}

/* ==================== Focus and Hover Synchronization ==================== */

/* Consistent focus styles */
.navbar-menu-toggle:focus,
.navbar-action:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Consistent hover effects */
.navbar-menu-toggle:hover,
.navbar-action:hover {
    background-color: var(--bg-secondary);
    transform: scale(1.05);
}

/* ==================== Debug Helpers (Remove in production) ==================== */

/* Uncomment for debugging alignment issues */
/*
.navbar {
    border: 2px solid red !important;
}

.sidebar-brand {
    border: 2px solid blue !important;
}

.navbar-menu-toggle {
    border: 1px solid green !important;
}
*/
