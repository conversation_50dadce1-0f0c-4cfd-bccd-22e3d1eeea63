/**
 * Invoices Page JavaScript
 * Handles invoice templates and printing
 */

// Sample invoice data
const sampleInvoiceData = {
    invoiceNumber: 'INV-2025-001',
    date: '2025-09-30',
    dueDate: '2025-10-30',
    company: {
        name: 'شركة التقنية المتقدمة',
        address: 'شارع الملك فهد، الرياض 12345',
        phone: '+966 11 234 5678',
        email: '<EMAIL>',
        taxNumber: '300123456789003'
    },
    customer: {
        name: 'شركة العميل المحترم',
        address: 'شارع العليا، الرياض 54321',
        phone: '+966 11 876 5432',
        email: '<EMAIL>',
        taxNumber: '300987654321003'
    },
    items: [
        { id: 1, description: 'لابتوب Dell XPS 15', quantity: 2, price: 4500, total: 9000 },
        { id: 2, description: 'ماوس لاسلكي Logitech', quantity: 5, price: 150, total: 750 },
        { id: 3, description: 'لوحة مفاتيح ميكانيكية', quantity: 3, price: 350, total: 1050 },
        { id: 4, description: 'شاشة Samsung 27 بوصة', quantity: 2, price: 1200, total: 2400 }
    ],
    subtotal: 13200,
    taxRate: 15,
    tax: 1980,
    total: 15180,
    notes: 'شكراً لتعاملكم معنا. يرجى السداد خلال 30 يوم من تاريخ الفاتورة.'
};

// Initialize Toastr
toastr.options = {
    closeButton: true,
    progressBar: true,
    positionClass: 'toast-top-left',
    rtl: true,
    timeOut: 3000
};

// Document Ready
$(document).ready(function() {
    // Create invoice button
    $('#btnCreateInvoice').click(function() {
        showCreateInvoiceDialog();
    });
});

// Show invoice based on template type
function showInvoice(templateType) {
    let invoiceHTML = '';

    switch(templateType) {
        case 'classic':
            invoiceHTML = generateClassicInvoice();
            break;
        case 'modern':
            invoiceHTML = generateModernInvoice();
            break;
        case 'minimal':
            invoiceHTML = generateMinimalInvoice();
            break;
        case 'colorful':
            invoiceHTML = generateColorfulInvoice();
            break;
        case 'elegant':
            invoiceHTML = generateElegantInvoice();
            break;
        case 'tax':
            invoiceHTML = generateTaxInvoice();
            break;
        case 'receipt':
            invoiceHTML = generateReceiptInvoice();
            break;
        default:
            invoiceHTML = generateClassicInvoice();
    }
    
    // Show invoice in modal
    Swal.fire({
        html: invoiceHTML,
        width: '1000px',
        showCloseButton: true,
        showCancelButton: true,
        confirmButtonText: '<i class="fas fa-print"></i> طباعة',
        cancelButtonText: '<i class="fas fa-download"></i> تحميل PDF',
        customClass: {
            popup: 'invoice-modal',
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-success'
        },
        didOpen: () => {
            // Add print button functionality
            const confirmButton = Swal.getConfirmButton();
            confirmButton.onclick = () => {
                printInvoice();
            };
            
            // Add download button functionality
            const cancelButton = Swal.getCancelButton();
            cancelButton.onclick = () => {
                downloadInvoice();
            };
        }
    });
}

// Generate Classic Invoice
function generateClassicInvoice() {
    return `
        <div class="invoice-container invoice-print-area">
            <div class="invoice-header">
                <div class="company-info">
                    <h2>${sampleInvoiceData.company.name}</h2>
                    <p>${sampleInvoiceData.company.address}</p>
                    <p>هاتف: ${sampleInvoiceData.company.phone}</p>
                    <p>بريد: ${sampleInvoiceData.company.email}</p>
                </div>
                <div class="invoice-meta">
                    <div class="invoice-number">فاتورة #${sampleInvoiceData.invoiceNumber}</div>
                    <p>التاريخ: ${sampleInvoiceData.date}</p>
                    <p>تاريخ الاستحقاق: ${sampleInvoiceData.dueDate}</p>
                </div>
            </div>
            
            <div class="invoice-details">
                <div class="detail-section">
                    <h5>الفاتورة من:</h5>
                    <p><strong>${sampleInvoiceData.company.name}</strong></p>
                    <p>${sampleInvoiceData.company.address}</p>
                    <p>${sampleInvoiceData.company.phone}</p>
                    <p>${sampleInvoiceData.company.email}</p>
                </div>
                <div class="detail-section">
                    <h5>الفاتورة إلى:</h5>
                    <p><strong>${sampleInvoiceData.customer.name}</strong></p>
                    <p>${sampleInvoiceData.customer.address}</p>
                    <p>${sampleInvoiceData.customer.phone}</p>
                    <p>${sampleInvoiceData.customer.email}</p>
                </div>
            </div>
            
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الوصف</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${sampleInvoiceData.items.map((item, index) => `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${item.description}</td>
                            <td>${item.quantity}</td>
                            <td>${item.price.toFixed(2)} ر.س</td>
                            <td>${item.total.toFixed(2)} ر.س</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            
            <div class="invoice-totals">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>${sampleInvoiceData.subtotal.toFixed(2)} ر.س</span>
                </div>
                <div class="total-row">
                    <span>الضريبة (${sampleInvoiceData.taxRate}%):</span>
                    <span>${sampleInvoiceData.tax.toFixed(2)} ر.س</span>
                </div>
                <div class="total-row grand-total">
                    <span>الإجمالي:</span>
                    <span>${sampleInvoiceData.total.toFixed(2)} ر.س</span>
                </div>
            </div>
            
            <div class="invoice-notes">
                <h6>ملاحظات:</h6>
                <p>${sampleInvoiceData.notes}</p>
            </div>
            
            <div class="invoice-footer">
                <p>شكراً لتعاملكم معنا</p>
                <p>${sampleInvoiceData.company.name} - ${sampleInvoiceData.company.phone}</p>
            </div>
        </div>
    `;
}

// Generate Modern Invoice
function generateModernInvoice() {
    return `
        <div class="invoice-container invoice-modern invoice-print-area">
            <div class="invoice-header">
                <div class="company-info">
                    <h2>${sampleInvoiceData.company.name}</h2>
                    <p>${sampleInvoiceData.company.address}</p>
                    <p>هاتف: ${sampleInvoiceData.company.phone} | بريد: ${sampleInvoiceData.company.email}</p>
                </div>
                <div class="invoice-meta">
                    <div class="invoice-number">فاتورة #${sampleInvoiceData.invoiceNumber}</div>
                    <p>التاريخ: ${sampleInvoiceData.date}</p>
                </div>
            </div>
            
            <div class="invoice-details">
                <div class="detail-section">
                    <h5>الفاتورة إلى:</h5>
                    <p><strong>${sampleInvoiceData.customer.name}</strong></p>
                    <p>${sampleInvoiceData.customer.address}</p>
                    <p>${sampleInvoiceData.customer.phone}</p>
                </div>
                <div class="detail-section">
                    <h5>تفاصيل الدفع:</h5>
                    <p>تاريخ الاستحقاق: ${sampleInvoiceData.dueDate}</p>
                    <p>طريقة الدفع: تحويل بنكي</p>
                </div>
            </div>
            
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>الوصف</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${sampleInvoiceData.items.map(item => `
                        <tr>
                            <td><strong>${item.description}</strong></td>
                            <td>${item.quantity}</td>
                            <td>${item.price.toFixed(2)} ر.س</td>
                            <td>${item.total.toFixed(2)} ر.س</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            
            <div class="invoice-totals">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>${sampleInvoiceData.subtotal.toFixed(2)} ر.س</span>
                </div>
                <div class="total-row">
                    <span>الضريبة (${sampleInvoiceData.taxRate}%):</span>
                    <span>${sampleInvoiceData.tax.toFixed(2)} ر.س</span>
                </div>
                <div class="total-row grand-total">
                    <span>الإجمالي:</span>
                    <span>${sampleInvoiceData.total.toFixed(2)} ر.س</span>
                </div>
            </div>
            
            <div class="invoice-footer">
                <p><strong>شكراً لتعاملكم معنا!</strong></p>
            </div>
        </div>
    `;
}

// Generate Minimal Invoice
function generateMinimalInvoice() {
    return `
        <div class="invoice-container invoice-minimal invoice-print-area">
            <div class="invoice-header">
                <div class="company-info">
                    <h2>${sampleInvoiceData.company.name}</h2>
                </div>
                <div class="invoice-meta">
                    <div class="invoice-number">#${sampleInvoiceData.invoiceNumber}</div>
                    <p>${sampleInvoiceData.date}</p>
                </div>
            </div>
            
            <div style="margin-bottom: 2rem;">
                <p><strong>إلى:</strong> ${sampleInvoiceData.customer.name}</p>
            </div>
            
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>البند</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    ${sampleInvoiceData.items.map(item => `
                        <tr>
                            <td>${item.description}</td>
                            <td>${item.quantity}</td>
                            <td>${item.price.toFixed(2)}</td>
                            <td>${item.total.toFixed(2)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            
            <div class="invoice-totals">
                <div class="total-row grand-total">
                    <span>الإجمالي:</span>
                    <span>${sampleInvoiceData.total.toFixed(2)} ر.س</span>
                </div>
            </div>
        </div>
    `;
}

// Generate Colorful Invoice
function generateColorfulInvoice() {
    return `
        <div class="invoice-container invoice-colorful invoice-print-area">
            <div class="invoice-header">
                <div class="company-info">
                    <h2>${sampleInvoiceData.company.name}</h2>
                    <p>${sampleInvoiceData.company.address}</p>
                    <p>${sampleInvoiceData.company.phone}</p>
                </div>
                <div class="invoice-meta">
                    <div class="invoice-number">فاتورة #${sampleInvoiceData.invoiceNumber}</div>
                    <p>التاريخ: ${sampleInvoiceData.date}</p>
                </div>
            </div>
            
            <div class="invoice-details">
                <div class="detail-section">
                    <h5>العميل:</h5>
                    <p><strong>${sampleInvoiceData.customer.name}</strong></p>
                    <p>${sampleInvoiceData.customer.address}</p>
                </div>
                <div class="detail-section">
                    <h5>معلومات الدفع:</h5>
                    <p>الاستحقاق: ${sampleInvoiceData.dueDate}</p>
                </div>
            </div>
            
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الوصف</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${sampleInvoiceData.items.map((item, index) => `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${item.description}</td>
                            <td>${item.quantity}</td>
                            <td>${item.price.toFixed(2)} ر.س</td>
                            <td>${item.total.toFixed(2)} ر.س</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            
            <div class="invoice-totals">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>${sampleInvoiceData.subtotal.toFixed(2)} ر.س</span>
                </div>
                <div class="total-row">
                    <span>الضريبة (${sampleInvoiceData.taxRate}%):</span>
                    <span>${sampleInvoiceData.tax.toFixed(2)} ر.س</span>
                </div>
                <div class="total-row grand-total">
                    <span>الإجمالي:</span>
                    <span>${sampleInvoiceData.total.toFixed(2)} ر.س</span>
                </div>
            </div>
            
            <div class="invoice-footer">
                <p>شكراً لتعاملكم معنا</p>
            </div>
        </div>
    `;
}

// Generate Elegant Invoice
function generateElegantInvoice() {
    return `
        <div class="invoice-container invoice-elegant invoice-print-area">
            <div class="invoice-header">
                <div class="company-info">
                    <h2 style="font-size: 2rem;">${sampleInvoiceData.company.name}</h2>
                    <p style="font-style: italic;">${sampleInvoiceData.company.address}</p>
                </div>
                <div class="invoice-meta">
                    <div class="invoice-number" style="font-size: 1.75rem;">فاتورة</div>
                    <p>#${sampleInvoiceData.invoiceNumber}</p>
                    <p>${sampleInvoiceData.date}</p>
                </div>
            </div>
            
            <div class="invoice-details">
                <div class="detail-section">
                    <h5>الفاتورة إلى:</h5>
                    <p><strong>${sampleInvoiceData.customer.name}</strong></p>
                    <p>${sampleInvoiceData.customer.address}</p>
                    <p>${sampleInvoiceData.customer.phone}</p>
                </div>
                <div class="detail-section">
                    <h5>تفاصيل:</h5>
                    <p>تاريخ الإصدار: ${sampleInvoiceData.date}</p>
                    <p>تاريخ الاستحقاق: ${sampleInvoiceData.dueDate}</p>
                </div>
            </div>
            
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>البند</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    ${sampleInvoiceData.items.map(item => `
                        <tr>
                            <td><em>${item.description}</em></td>
                            <td>${item.quantity}</td>
                            <td>${item.price.toFixed(2)} ر.س</td>
                            <td><strong>${item.total.toFixed(2)} ر.س</strong></td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            
            <div class="invoice-totals">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>${sampleInvoiceData.subtotal.toFixed(2)} ر.س</span>
                </div>
                <div class="total-row">
                    <span>الضريبة (${sampleInvoiceData.taxRate}%):</span>
                    <span>${sampleInvoiceData.tax.toFixed(2)} ر.س</span>
                </div>
                <div class="total-row grand-total">
                    <span>الإجمالي الكلي:</span>
                    <span>${sampleInvoiceData.total.toFixed(2)} ر.س</span>
                </div>
            </div>
            
            <div class="invoice-notes">
                <h6>ملاحظات:</h6>
                <p style="font-style: italic;">${sampleInvoiceData.notes}</p>
            </div>
            
            <div class="invoice-footer">
                <p style="font-style: italic;">مع خالص الشكر والتقدير</p>
                <p><strong>${sampleInvoiceData.company.name}</strong></p>
            </div>
        </div>
    `;
}

// Generate Tax Invoice
function generateTaxInvoice() {
    return `
        <div class="invoice-container invoice-print-area">
            <div class="invoice-header">
                <div class="company-info">
                    <h2>${sampleInvoiceData.company.name}</h2>
                    <p>${sampleInvoiceData.company.address}</p>
                    <p>هاتف: ${sampleInvoiceData.company.phone}</p>
                    <p>الرقم الضريبي: ${sampleInvoiceData.company.taxNumber}</p>
                </div>
                <div class="invoice-meta">
                    <div class="invoice-number">فاتورة ضريبية</div>
                    <p>#${sampleInvoiceData.invoiceNumber}</p>
                    <p>التاريخ: ${sampleInvoiceData.date}</p>
                </div>
            </div>
            
            <div class="invoice-details">
                <div class="detail-section">
                    <h5>بيانات المورد:</h5>
                    <p><strong>${sampleInvoiceData.company.name}</strong></p>
                    <p>${sampleInvoiceData.company.address}</p>
                    <p>الرقم الضريبي: ${sampleInvoiceData.company.taxNumber}</p>
                </div>
                <div class="detail-section">
                    <h5>بيانات العميل:</h5>
                    <p><strong>${sampleInvoiceData.customer.name}</strong></p>
                    <p>${sampleInvoiceData.customer.address}</p>
                    <p>الرقم الضريبي: ${sampleInvoiceData.customer.taxNumber}</p>
                </div>
            </div>
            
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الوصف</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                        <th>الضريبة</th>
                        <th>المجموع شامل الضريبة</th>
                    </tr>
                </thead>
                <tbody>
                    ${sampleInvoiceData.items.map((item, index) => {
                        const itemTax = item.total * (sampleInvoiceData.taxRate / 100);
                        const itemTotalWithTax = item.total + itemTax;
                        return `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${item.description}</td>
                                <td>${item.quantity}</td>
                                <td>${item.price.toFixed(2)} ر.س</td>
                                <td>${item.total.toFixed(2)} ر.س</td>
                                <td>${itemTax.toFixed(2)} ر.س</td>
                                <td>${itemTotalWithTax.toFixed(2)} ر.س</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
            
            <div class="invoice-totals">
                <div class="total-row">
                    <span>المجموع قبل الضريبة:</span>
                    <span>${sampleInvoiceData.subtotal.toFixed(2)} ر.س</span>
                </div>
                <div class="total-row">
                    <span>ضريبة القيمة المضافة (${sampleInvoiceData.taxRate}%):</span>
                    <span>${sampleInvoiceData.tax.toFixed(2)} ر.س</span>
                </div>
                <div class="total-row grand-total">
                    <span>الإجمالي شامل الضريبة:</span>
                    <span>${sampleInvoiceData.total.toFixed(2)} ر.س</span>
                </div>
            </div>
            
            <div class="invoice-notes">
                <h6>ملاحظات:</h6>
                <p>هذه فاتورة ضريبية صادرة وفقاً لأنظمة ضريبة القيمة المضافة في المملكة العربية السعودية.</p>
                <p>${sampleInvoiceData.notes}</p>
            </div>
            
            <div class="invoice-footer">
                <p>فاتورة ضريبية معتمدة</p>
                <p>${sampleInvoiceData.company.name} - الرقم الضريبي: ${sampleInvoiceData.company.taxNumber}</p>
            </div>
        </div>
    `;
}

// Generate Receipt/Grocery Invoice
function generateReceiptInvoice() {
    const currentDate = new Date().toLocaleString('ar-SA');
    return `
        <div class="invoice-container invoice-receipt invoice-print-area">
            <div class="invoice-header">
                <div class="company-info">
                    <h2>${sampleInvoiceData.company.name}</h2>
                    <p>${sampleInvoiceData.company.address}</p>
                    <p>هاتف: ${sampleInvoiceData.company.phone}</p>
                    <p>الرقم الضريبي: ${sampleInvoiceData.company.taxNumber}</p>
                </div>
                <div class="invoice-meta">
                    <p>فاتورة رقم: ${sampleInvoiceData.invoiceNumber}</p>
                    <p>${currentDate}</p>
                </div>
            </div>

            <table class="invoice-table">
                <thead>
                    <tr>
                        <th style="text-align: right;">الصنف</th>
                        <th style="text-align: center;">الكمية</th>
                        <th style="text-align: left;">السعر</th>
                    </tr>
                </thead>
                <tbody>
                    ${sampleInvoiceData.items.map(item => `
                        <tr>
                            <td style="text-align: right;">${item.description}</td>
                            <td style="text-align: center;">${item.quantity}</td>
                            <td style="text-align: left;">${item.total.toFixed(2)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>

            <div class="invoice-totals">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>${sampleInvoiceData.subtotal.toFixed(2)} ر.س</span>
                </div>
                <div class="total-row">
                    <span>الضريبة (${sampleInvoiceData.taxRate}%):</span>
                    <span>${sampleInvoiceData.tax.toFixed(2)} ر.س</span>
                </div>
                <div class="total-row grand-total">
                    <span>الإجمالي:</span>
                    <span>${sampleInvoiceData.total.toFixed(2)} ر.س</span>
                </div>
            </div>

            <div class="barcode">
                *${sampleInvoiceData.invoiceNumber}*
            </div>

            <div class="invoice-footer">
                <p>*** شكراً لزيارتكم ***</p>
                <p>البضاعة المباعة لا ترد ولا تستبدل</p>
                <p>يرجى الاحتفاظ بالفاتورة</p>
                <p>${sampleInvoiceData.company.phone}</p>
            </div>
        </div>
    `;
}

// Print invoice
function printInvoice() {
    // Get the invoice content
    const invoiceContent = document.querySelector('.invoice-print-area');

    if (!invoiceContent) {
        toastr.error('لم يتم العثور على الفاتورة', 'خطأ');
        return;
    }

    // Create a new window for printing
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // Write the invoice content to the new window
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>طباعة الفاتورة</title>
            <link rel="stylesheet" href="../assets/css/fonts.css">
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: 'Cairo', sans-serif;
                    direction: rtl;
                    padding: 20px;
                    background: white;
                }

                /* Copy all invoice styles */
                ${getInvoiceStyles()}

                @media print {
                    body {
                        padding: 0;
                    }

                    @page {
                        margin: 1cm;
                    }
                }
            </style>
        </head>
        <body>
            ${invoiceContent.outerHTML}
            <script>
                // Auto print when loaded
                window.onload = function() {
                    window.print();
                    // Close window after printing (optional)
                    // setTimeout(() => window.close(), 100);
                };
            </script>
        </body>
        </html>
    `);

    printWindow.document.close();
    toastr.success('جاري فتح نافذة الطباعة...', 'طباعة');
}

// Get invoice styles
function getInvoiceStyles() {
    return `
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
        }

        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #333;
        }

        .company-info h2 {
            font-size: 1.75rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .company-info p {
            color: #666;
            margin: 0.25rem 0;
        }

        .invoice-meta {
            text-align: left;
        }

        .invoice-meta p {
            margin: 0.25rem 0;
            color: #666;
        }

        .invoice-meta strong {
            color: #333;
        }

        .customer-info {
            background: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }

        .customer-info h3 {
            font-size: 1.125rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .customer-info p {
            color: #666;
            margin: 0.25rem 0;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 2rem;
        }

        .invoice-table th {
            background: #6366f1;
            color: white;
            padding: 0.75rem;
            text-align: right;
            font-weight: 600;
        }

        .invoice-table td {
            padding: 0.75rem;
            border-bottom: 1px solid #e5e7eb;
            color: #333;
        }

        .invoice-table tbody tr:hover {
            background: #f9fafb;
        }

        .invoice-totals {
            max-width: 400px;
            margin-right: auto;
            margin-bottom: 2rem;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            color: #666;
        }

        .total-row.subtotal {
            border-top: 1px solid #e5e7eb;
            padding-top: 1rem;
        }

        .total-row.grand-total {
            border-top: 2px solid #333;
            margin-top: 0.5rem;
            padding-top: 1rem;
            font-size: 1.25rem;
            font-weight: 700;
            color: #6366f1;
        }

        .invoice-notes {
            background: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }

        .invoice-notes h4 {
            font-size: 1rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .invoice-notes p {
            color: #666;
            line-height: 1.6;
        }

        .invoice-footer {
            text-align: center;
            padding-top: 2rem;
            border-top: 2px solid #e5e7eb;
            color: #666;
        }

        .invoice-footer p {
            margin: 0.25rem 0;
        }

        /* Modern Invoice Styles */
        .invoice-modern .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin: -2rem -2rem 2rem -2rem;
        }

        .invoice-modern .company-info h2,
        .invoice-modern .company-info p,
        .invoice-modern .invoice-meta p,
        .invoice-modern .invoice-meta strong {
            color: white;
        }

        /* Minimal Invoice Styles */
        .invoice-minimal {
            border: 1px solid #e5e7eb;
        }

        .invoice-minimal .invoice-header {
            border-bottom: 1px solid #e5e7eb;
        }

        .invoice-minimal .invoice-table th {
            background: #f9fafb;
            color: #333;
            border: 1px solid #e5e7eb;
        }

        .invoice-minimal .invoice-table td {
            border: 1px solid #e5e7eb;
        }

        /* Colorful Invoice Styles */
        .invoice-colorful .invoice-table th {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        /* Elegant Invoice Styles */
        .invoice-elegant {
            border: 3px double #333;
            padding: 3rem;
        }

        .invoice-elegant .invoice-header {
            border-bottom: 3px double #6366f1;
        }

        /* Receipt/Grocery Style */
        .invoice-receipt {
            max-width: 400px;
            margin: 0 auto;
            font-family: 'Courier New', monospace;
            background: white;
            padding: 1rem;
        }

        .invoice-receipt .invoice-header {
            text-align: center;
            border-bottom: 2px dashed #000;
            padding-bottom: 1rem;
            margin-bottom: 1rem;
        }

        .invoice-receipt .company-info h2 {
            font-size: 1.5rem;
            margin-bottom: 0.25rem;
        }

        .invoice-receipt .company-info p {
            font-size: 0.875rem;
            margin: 0.125rem 0;
        }

        .invoice-receipt .invoice-meta {
            text-align: center;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }

        .invoice-receipt .invoice-table {
            margin-bottom: 1rem;
        }

        .invoice-receipt .invoice-table th,
        .invoice-receipt .invoice-table td {
            padding: 0.25rem 0;
            font-size: 0.875rem;
            border: none;
        }

        .invoice-receipt .invoice-table thead {
            border-bottom: 1px dashed #000;
        }

        .invoice-receipt .invoice-table tbody tr {
            border-bottom: 1px dotted #ccc;
        }

        .invoice-receipt .invoice-totals {
            width: 100%;
            border-top: 2px dashed #000;
            padding-top: 0.5rem;
        }

        .invoice-receipt .total-row {
            font-size: 0.875rem;
        }

        .invoice-receipt .total-row.grand-total {
            font-size: 1.25rem;
            border-top: 2px solid #000;
            margin-top: 0.5rem;
            padding-top: 0.5rem;
        }

        .invoice-receipt .invoice-footer {
            text-align: center;
            border-top: 2px dashed #000;
            padding-top: 1rem;
            margin-top: 1rem;
            font-size: 0.75rem;
        }

        .invoice-receipt .barcode {
            text-align: center;
            margin: 1rem 0;
            font-family: 'Libre Barcode 128', cursive;
            font-size: 3rem;
            letter-spacing: 0;
        }
    `;
}

// Download invoice as PDF (simulation)
function downloadInvoice() {
    toastr.info('جاري تحميل الفاتورة...', 'تحميل');
    
    setTimeout(() => {
        toastr.success('تم تحميل الفاتورة بنجاح!', 'نجح!');
    }, 1500);
}

// Show create invoice dialog
function showCreateInvoiceDialog() {
    Swal.fire({
        title: 'إنشاء فاتورة جديدة',
        text: 'اختر نوع الفاتورة التي تريد إنشاءها',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'فاتورة عادية',
        cancelButtonText: 'فاتورة ضريبية',
        showDenyButton: true,
        denyButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            showInvoice('classic');
        } else if (result.dismiss === Swal.DismissReason.cancel) {
            showInvoice('tax');
        }
    });
}

