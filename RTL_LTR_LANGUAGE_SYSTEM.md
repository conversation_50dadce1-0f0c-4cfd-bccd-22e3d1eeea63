# نظام دعم RTL/LTR مع تبديل اللغة | RTL/LTR Language System

<div dir="rtl">

## 🌐 نظرة عامة

تم إضافة نظام كامل لدعم **اللغة العربية (RTL)** و **اللغة الإنجليزية (LTR)** مع إمكانية التبديل بينهما بسهولة.

---

## ✨ الميزات الجديدة

### 🔄 زر تبديل اللغة
- **الموقع:** شريط التنقل العلوي (Navbar)
- **الأيقونة:** 🌐 (Globe icon)
- **الوظيفة:** تبديل فوري بين العربية والإنجليزية

### 🎨 دعم التصميم الثنائي
- **RTL:** تصميم من اليمين إلى اليسار للعربية
- **LTR:** تصميم من اليسار إلى اليمين للإنجليزية
- **Bootstrap:** تبديل تلقائي بين `bootstrap.rtl.min.css` و `bootstrap.min.css`

### 💾 حفظ الإعدادات
- **localStorage:** حفظ اللغة المختارة
- **استمرارية:** الاحتفاظ بالإعدادات عند إعادة تحميل الصفحة

### 🔤 نظام الترجمة
- **نصوص ثنائية:** دعم النصوص العربية والإنجليزية
- **ترجمة فورية:** تحديث جميع النصوص عند تغيير اللغة

---

## 📁 الملفات الجديدة والمعدلة

### 1. ملفات جديدة:

#### `assets/css/rtl-ltr.css`
**الغرض:** دعم شامل لـ RTL/LTR

**المحتوى:**
- قواعد CSS للاتجاه LTR
- إعادة تموضع العناصر للإنجليزية
- دعم الخطوط المختلفة
- تحسينات للمكونات

**الحجم:** 280+ سطر

---

### 2. ملفات معدلة:

#### `index.html`
**التعديلات:**
- ✅ إضافة زر تبديل اللغة في الـ navbar
- ✅ إضافة `assets/css/rtl-ltr.css`
- ✅ إضافة عناصر `.lang-ar` و `.lang-en`
- ✅ إضافة dropdown للغات

#### `assets/js/main.js`
**التعديلات:**
- ✅ إضافة نظام الترجمة الكامل
- ✅ وظائف تبديل اللغة
- ✅ حفظ/استرجاع الإعدادات من localStorage
- ✅ تحديث Bootstrap CSS تلقائياً

#### `assets/css/navbar.css`
**التعديلات:**
- ✅ إضافة أنماط زر تبديل اللغة
- ✅ تحسينات للشاشات الصغيرة

#### `assets/libs/bootstrap/css/`
**الإضافة:**
- ✅ `bootstrap.min.css` (للإنجليزية LTR)
- ✅ `bootstrap.rtl.min.css` (للعربية RTL - موجود مسبقاً)

---

## 🎯 كيفية الاستخدام

### 1. تبديل اللغة:
```
1. انقر على أيقونة 🌐 في شريط التنقل
2. اختر اللغة المطلوبة:
   - العربية (Arabic - RTL)
   - English (LTR)
3. سيتم التبديل فوراً مع حفظ الإعداد
```

### 2. إضافة نصوص جديدة:
```html
<!-- في HTML -->
<span class="nav-link-text">
    <span class="lang-ar">النص العربي</span>
    <span class="lang-en">English Text</span>
</span>
```

```javascript
// في JavaScript
const translations = {
    ar: {
        newText: 'النص الجديد'
    },
    en: {
        newText: 'New Text'
    }
};
```

### 3. إضافة عناصر جديدة تدعم RTL/LTR:
```css
/* في CSS */
[dir="ltr"] .new-element {
    /* قواعد للإنجليزية */
    text-align: left;
    margin-left: 1rem;
}

[dir="rtl"] .new-element {
    /* قواعد للعربية */
    text-align: right;
    margin-right: 1rem;
}
```

---

## 🔧 التفاصيل التقنية

### نظام الترجمة:

#### 1. **الهيكل:**
```javascript
const translations = {
    ar: {
        // النصوص العربية
        dashboard: 'لوحة التحكم',
        reports: 'التقارير'
    },
    en: {
        // النصوص الإنجليزية
        dashboard: 'Dashboard',
        reports: 'Reports'
    }
};
```

#### 2. **الوظائف الرئيسية:**
- `getCurrentLanguage()` - الحصول على اللغة الحالية
- `setLanguage(lang, dir)` - تعيين اللغة والاتجاه
- `updateTextContent(lang)` - تحديث النصوص
- `initLanguage()` - تهيئة النظام

#### 3. **حفظ الإعدادات:**
```javascript
// حفظ
localStorage.setItem('language', 'ar');
localStorage.setItem('direction', 'rtl');

// استرجاع
const lang = localStorage.getItem('language') || 'ar';
const dir = localStorage.getItem('direction') || 'rtl';
```

---

### دعم Bootstrap:

#### 1. **التبديل التلقائي:**
```javascript
if (dir === 'rtl') {
    $('link[href*="bootstrap"]').attr('href', 'assets/libs/bootstrap/css/bootstrap.rtl.min.css');
} else {
    $('link[href*="bootstrap"]').attr('href', 'assets/libs/bootstrap/css/bootstrap.min.css');
}
```

#### 2. **الملفات المطلوبة:**
- `bootstrap.rtl.min.css` - للعربية (RTL)
- `bootstrap.min.css` - للإنجليزية (LTR)

---

### دعم CSS:

#### 1. **قواعد الاتجاه:**
```css
/* للعربية (افتراضي) */
.sidebar {
    right: 0;
}

/* للإنجليزية */
[dir="ltr"] .sidebar {
    right: auto;
    left: 0;
}
```

#### 2. **الخطوط:**
```css
[dir="rtl"] {
    font-family: var(--font-family); /* Cairo */
}

[dir="ltr"] {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
```

---

## 📱 الدعم المتجاوب

### الشاشات الكبيرة (> 992px):
- ✅ زر اللغة مع النص والأيقونة
- ✅ dropdown كامل للغات
- ✅ جميع العناصر تدعم RTL/LTR

### الشاشات المتوسطة (768px - 991px):
- ✅ زر اللغة مع الأيقونة فقط
- ✅ dropdown مبسط
- ✅ تخطيط متجاوب

### الشاشات الصغيرة (< 768px):
- ✅ زر اللغة مدمج
- ✅ dropdown محسن للمس
- ✅ تجربة مستخدم محسنة

---

## 🎨 التحسينات البصرية

### 1. **انتقالات سلسة:**
```css
.language-switching {
    transition: all 0.3s ease-in-out;
}
```

### 2. **مؤشرات بصرية:**
- ✅ علامة ✓ للغة النشطة
- ✅ تأثيرات hover
- ✅ أيقونات ملونة

### 3. **تجربة مستخدم:**
- ✅ تبديل فوري
- ✅ حفظ تلقائي
- ✅ استرجاع عند إعادة التحميل

---

## 🧪 الاختبار

### 1. **اختبار التبديل:**
```
✅ انقر على زر اللغة
✅ اختر العربية - تحقق من RTL
✅ اختر الإنجليزية - تحقق من LTR
✅ أعد تحميل الصفحة - تحقق من الحفظ
```

### 2. **اختبار العناصر:**
```
✅ Sidebar - الموقع والاتجاه
✅ Navbar - الموقع والمحتوى
✅ النصوص - الترجمة الصحيحة
✅ الأزرار - الأيقونات والنصوص
✅ النماذج - الاتجاه والتخطيط
```

### 3. **اختبار الشاشات:**
```
✅ شاشة كبيرة (1920px)
✅ شاشة متوسطة (1024px)
✅ شاشة صغيرة (768px)
✅ هاتف (375px)
```

---

## 📊 الإحصائيات

### الملفات:
- **جديد:** 1 ملف CSS
- **معدل:** 3 ملفات (HTML, JS, CSS)
- **مضاف:** 1 ملف Bootstrap

### الكود:
- **CSS جديد:** 280+ سطر
- **JavaScript جديد:** 235+ سطر
- **HTML معدل:** 50+ سطر

### الميزات:
- **لغات مدعومة:** 2 (العربية، الإنجليزية)
- **اتجاهات:** RTL + LTR
- **عناصر مترجمة:** 20+ عنصر
- **مكونات محسنة:** جميع المكونات

---

## 🎯 الخطوات التالية

### 1. **إضافة لغات جديدة:**
```javascript
// إضافة الفرنسية مثلاً
const translations = {
    ar: { /* العربية */ },
    en: { /* الإنجليزية */ },
    fr: { /* الفرنسية */ }
};
```

### 2. **تحسينات إضافية:**
- إضافة أعلام الدول
- تحسين الانتقالات
- دعم لغات إضافية
- تحسين الأداء

### 3. **تطبيق على الصفحات الأخرى:**
- نسخ النظام لجميع الصفحات
- توحيد التجربة
- اختبار شامل

---

## ✅ قائمة التحقق

- [x] إضافة زر تبديل اللغة
- [x] إنشاء نظام الترجمة
- [x] دعم RTL/LTR في CSS
- [x] تبديل Bootstrap تلقائياً
- [x] حفظ الإعدادات في localStorage
- [x] تحديث النصوص ديناميكياً
- [x] دعم الشاشات المختلفة
- [x] اختبار النظام
- [x] كتابة التوثيق

---

## 🎊 النتيجة النهائية

### ✅ تم إنجاز النظام بالكامل!

**الميزات المضافة:**
- 🌐 زر تبديل اللغة في الـ navbar
- 🔄 دعم كامل لـ RTL/LTR
- 💾 حفظ الإعدادات تلقائياً
- 🎨 تصميم متجاوب ومحسن
- 🔤 نظام ترجمة شامل

**جاهز للاستخدام! 🚀**

</div>
